# 🐳 TravelEase Docker Setup Guide

Complete Docker containerization for the TravelEase travel booking platform with custom ports to avoid conflicts.

## 📋 Prerequisites

- **Docker** (v20.10 or higher)
- **Docker Compose** (v2.0 or higher)
- **Git** (for cloning the repository)
- **8GB RAM** minimum (recommended: 16GB)
- **10GB free disk space**

### Install Docker

**macOS:**
```bash
brew install docker docker-compose
# or download Docker Desktop from https://docker.com
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install docker.io docker-compose
sudo usermod -aG docker $USER
```

**Windows:**
Download Docker Desktop from https://docker.com

## 🚀 Quick Start

### 1. Clone and Setup
```bash
git clone <repository-url>
cd travelease
chmod +x scripts/*.sh
./scripts/docker-setup.sh
```

### 2. Access Applications
- **API Server**: http://localhost:8001
- **Admin Portal**: http://localhost:8002
- **Agent Portal**: http://localhost:8003
- **Consumer App**: http://localhost:8004

## 🔧 Custom Port Configuration

We use custom ports to avoid conflicts with commonly used default ports:

| Service | Default Port | Custom Port | Reason |
|---------|-------------|-------------|---------|
| API Server | 3000 | 8001 | Avoid Node.js default |
| Admin Portal | 3000 | 8002 | Avoid React default |
| Agent Portal | 3000 | 8003 | Avoid React default |
| Consumer App | 19006 | 8004 | Avoid Expo default |
| Expo Dev Server | 19000 | 8005 | Avoid Expo default |
| Expo Dev Tools | 19001 | 8006 | Avoid Expo default |

## 📁 Docker Architecture

```
travelease/
├── docker-compose.yml          # Production configuration
├── docker-compose.dev.yml      # Development overrides
├── .env.example               # Environment template
├── scripts/
│   ├── docker-setup.sh        # Production setup
│   ├── docker-dev.sh          # Development setup
│   └── test-guide.sh          # Testing guide
├── api-server/
│   ├── Dockerfile             # Production image
│   ├── Dockerfile.dev         # Development image
│   └── .dockerignore
├── admin-portal/
│   ├── Dockerfile             # Multi-stage build
│   ├── Dockerfile.dev         # Development image
│   ├── nginx.conf             # Nginx configuration
│   └── .dockerignore
├── agent-portal/
│   ├── Dockerfile             # Multi-stage build
│   ├── Dockerfile.dev         # Development image
│   ├── nginx.conf             # Nginx configuration
│   └── .dockerignore
└── consumer-app/
    ├── Dockerfile             # Expo web build
    └── .dockerignore
```

## 🛠️ Development Setup

For development with hot reload:

```bash
./scripts/docker-dev.sh
```

This provides:
- **Hot reload** for all applications
- **Source code mounting** for real-time changes
- **Development dependencies** included
- **Detailed logging** enabled

## 📋 Available Commands

### Setup Commands
```bash
# Production setup
./scripts/docker-setup.sh

# Development setup
./scripts/docker-dev.sh

# Testing guide
./scripts/test-guide.sh
```

### Docker Compose Commands
```bash
# Start all services
docker-compose up -d

# Start with logs
docker-compose up

# Stop all services
docker-compose down

# Restart services
docker-compose restart

# View logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f api-server

# Rebuild images
docker-compose build --no-cache

# Scale services (if needed)
docker-compose up -d --scale api-server=2
```

### Individual Service Commands
```bash
# Start specific service
docker-compose up -d api-server

# Stop specific service
docker-compose stop admin-portal

# Restart specific service
docker-compose restart agent-portal

# Execute commands in running container
docker-compose exec api-server npm run test
docker-compose exec admin-portal bash
```

## 🔍 Service Health Checks

All services include health checks:

```bash
# Check all services
docker-compose ps

# Check specific service health
curl http://localhost:8001/health  # API Server
curl http://localhost:8002         # Admin Portal
curl http://localhost:8003         # Agent Portal
curl http://localhost:8004         # Consumer App
```

## 🌍 Environment Configuration

### Environment Variables

Copy and customize the environment file:
```bash
cp .env.example .env
```

Key variables:
```env
# Ports
API_PORT=8001
ADMIN_PORTAL_PORT=8002
AGENT_PORTAL_PORT=8003
CONSUMER_APP_PORT=8004

# API Configuration
NODE_ENV=development
JWT_SECRET=your-secret-key

# Frontend URLs
REACT_APP_API_URL=http://localhost:8001
EXPO_PUBLIC_API_URL=http://localhost:8001
```

### Production vs Development

**Production** (`docker-compose.yml`):
- Optimized builds
- Nginx for static serving
- Minimal dependencies
- Production environment variables

**Development** (`docker-compose.dev.yml`):
- Hot reload enabled
- Source code mounted
- Development dependencies
- Debug logging

## 🧪 Testing Guide

Run the comprehensive testing guide:
```bash
./scripts/test-guide.sh
```

### Manual Testing Steps

1. **API Server** (http://localhost:8001)
   - Test health endpoint: `curl http://localhost:8001/health`
   - Test authentication
   - Test CRUD operations

2. **Admin Portal** (http://localhost:8002)
   - Login: <EMAIL> / admin123
   - Test agent management
   - Test dashboard functionality

3. **Agent Portal** (http://localhost:8003)
   - Login: <EMAIL> / agent123
   - Test package management
   - Test customer management
   - Test booking workflow

4. **Consumer App** (http://localhost:8004)
   - Login: <EMAIL> / customer123
   - Test trip dashboard
   - Test trip details
   - Test notifications

## 🐛 Troubleshooting

### Common Issues

**Port Conflicts:**
```bash
# Check what's using a port
lsof -i :8001
netstat -tulpn | grep 8001

# Kill process using port
kill -9 $(lsof -t -i:8001)
```

**Container Issues:**
```bash
# Remove all containers and start fresh
docker-compose down -v
docker system prune -a
docker-compose up --build
```

**Permission Issues:**
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh
```

**Memory Issues:**
```bash
# Check Docker memory usage
docker stats

# Increase Docker memory limit in Docker Desktop
# Settings > Resources > Memory > 8GB+
```

### Logs and Debugging

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f api-server

# Follow logs in real-time
docker-compose logs -f --tail=100

# Debug container
docker-compose exec api-server bash
docker-compose exec admin-portal sh
```

### Network Issues

```bash
# Check network connectivity
docker network ls
docker network inspect travelease_travelease-network

# Test inter-service communication
docker-compose exec admin-portal curl http://api-server:3000/health
```

## 🔒 Security Considerations

- **JWT Secret**: Change default JWT secret in production
- **CORS**: Configure proper CORS origins
- **Environment**: Use production environment variables
- **Secrets**: Use Docker secrets for sensitive data
- **Network**: Use custom networks for isolation

## 📈 Performance Optimization

### Production Optimizations

1. **Multi-stage builds** for smaller images
2. **Nginx** for static file serving
3. **Gzip compression** enabled
4. **Caching** for static assets
5. **Health checks** for reliability

### Development Optimizations

1. **Volume mounting** for hot reload
2. **Development dependencies** included
3. **Source maps** enabled
4. **Debug logging** available

## 🚀 Deployment

### Production Deployment

```bash
# Build production images
docker-compose build

# Start production services
docker-compose up -d

# Monitor services
docker-compose ps
docker-compose logs -f
```

### Scaling Services

```bash
# Scale API server
docker-compose up -d --scale api-server=3

# Use load balancer (nginx, traefik, etc.)
# Configure in docker-compose.yml
```

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section
2. Review service logs
3. Verify environment configuration
4. Test individual services
5. Check Docker and system resources

For additional help, refer to the main project documentation or create an issue in the repository.
