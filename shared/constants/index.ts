// API Configuration
export const API_BASE_URL = 'http://localhost:3001/api';

// Application Ports
export const PORTS = {
  API_SERVER: 3001,
  ADMIN_PORTAL: 3000,
  AGENT_PORTAL: 3002,
  CONSUMER_APP: 8081, // React Native Metro bundler default
} as const;

// Application URLs
export const APP_URLS = {
  ADMIN_PORTAL: `http://localhost:${PORTS.ADMIN_PORTAL}`,
  AGENT_PORTAL: `http://localhost:${PORTS.AGENT_PORTAL}`,
  API_SERVER: `http://localhost:${PORTS.API_SERVER}`,
} as const;

// User Roles
export const USER_ROLES = {
  SUPER_ADMIN: 'super_admin',
  TRAVEL_AGENT: 'travel_agent',
  CONSUMER: 'consumer',
} as const;

// Booking Status
export const BOOKING_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  CANCELLED: 'cancelled',
  COMPLETED: 'completed',
} as const;

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PARTIAL: 'partial',
  PAID: 'paid',
  REFUNDED: 'refunded',
} as const;

// Package Categories
export const PACKAGE_CATEGORIES = {
  ADVENTURE: 'adventure',
  RELAXATION: 'relaxation',
  CULTURAL: 'cultural',
  BUSINESS: 'business',
  FAMILY: 'family',
} as const;

// Activity Types
export const ACTIVITY_TYPES = {
  SIGHTSEEING: 'sightseeing',
  ADVENTURE: 'adventure',
  CULTURAL: 'cultural',
  LEISURE: 'leisure',
  DINING: 'dining',
} as const;

// Accommodation Types
export const ACCOMMODATION_TYPES = {
  HOTEL: 'hotel',
  RESORT: 'resort',
  HOSTEL: 'hostel',
  APARTMENT: 'apartment',
  GUESTHOUSE: 'guesthouse',
} as const;

// Transportation Types
export const TRANSPORTATION_TYPES = {
  FLIGHT: 'flight',
  TRAIN: 'train',
  BUS: 'bus',
  CAR: 'car',
  BOAT: 'boat',
} as const;

// Notification Types
export const NOTIFICATION_TYPES = {
  BOOKING_UPDATE: 'booking_update',
  PAYMENT_REMINDER: 'payment_reminder',
  TRIP_REMINDER: 'trip_reminder',
  SYSTEM_ALERT: 'system_alert',
  AGENT_MESSAGE: 'agent_message',
} as const;

// Priority Levels
export const PRIORITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
} as const;

// Default Credentials
export const DEFAULT_USERS = {
  SUPER_ADMIN: {
    email: '<EMAIL>',
    password: 'admin123',
  },
  TRAVEL_AGENT: {
    email: '<EMAIL>',
    password: 'agent123',
  },
  CONSUMER: {
    email: '<EMAIL>',
    password: 'user123',
  },
} as const;

// UI Constants
export const COLORS = {
  PRIMARY: '#4F46E5',
  SECONDARY: '#10B981',
  DANGER: '#EF4444',
  WARNING: '#F59E0B',
  INFO: '#3B82F6',
  SUCCESS: '#10B981',
  DARK: '#1F2937',
  LIGHT: '#F9FAFB',
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
} as const;

// File Upload
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM DD, YYYY',
  INPUT: 'YYYY-MM-DD',
  DATETIME: 'MMM DD, YYYY HH:mm',
  TIME: 'HH:mm',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'travelease_auth_token',
  USER_DATA: 'travelease_user_data',
  THEME: 'travelease_theme',
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REFRESH: '/auth/refresh',
  
  // Users
  USERS: '/users',
  PROFILE: '/users/profile',
  
  // Agents
  AGENTS: '/agents',
  AGENT_STATS: '/agents/stats',
  
  // Packages
  PACKAGES: '/packages',
  PACKAGE_SEARCH: '/packages/search',
  
  // Bookings
  BOOKINGS: '/bookings',
  BOOKING_STATS: '/bookings/stats',
  
  // Notifications
  NOTIFICATIONS: '/notifications',
  SEND_NOTIFICATION: '/notifications/send',
  
  // Dashboard
  ADMIN_DASHBOARD: '/dashboard/admin',
  AGENT_DASHBOARD: '/dashboard/agent',
  CONSUMER_DASHBOARD: '/dashboard/consumer',
  
  // File Upload
  UPLOAD: '/upload',
} as const;
