// User Types
export interface User {
  id: string;
  email: string;
  password: string;
  role: 'super_admin' | 'travel_agent' | 'consumer';
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SuperAdmin extends User {
  role: 'super_admin';
  permissions: string[];
}

export interface TravelAgent extends User {
  role: 'travel_agent';
  agencyName: string;
  licenseNumber: string;
  specializations: string[];
  rating: number;
  totalBookings: number;
  revenue: number;
  isActive: boolean;
}

export interface Consumer extends User {
  role: 'consumer';
  dateOfBirth?: string;
  address?: Address;
  emergencyContact?: EmergencyContact;
  preferences?: TravelPreferences;
}

// Address Type
export interface Address {
  street: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
}

// Emergency Contact
export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
}

// Travel Preferences
export interface TravelPreferences {
  budgetRange: 'budget' | 'mid-range' | 'luxury';
  travelStyle: 'adventure' | 'relaxation' | 'cultural' | 'business';
  accommodationType: 'hotel' | 'resort' | 'hostel' | 'apartment';
  dietaryRestrictions?: string[];
}

// Travel Package Types
export interface TravelPackage {
  id: string;
  name: string;
  description: string;
  destination: string;
  duration: number; // in days
  price: number;
  currency: string;
  category: 'adventure' | 'relaxation' | 'cultural' | 'business' | 'family';
  difficulty: 'easy' | 'moderate' | 'challenging';
  maxGroupSize: number;
  minAge: number;
  includes: string[];
  excludes: string[];
  images: string[];
  itinerary: ItineraryItem[];
  agentId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Itinerary Item
export interface ItineraryItem {
  id: string;
  day: number;
  title: string;
  description: string;
  activities: Activity[];
  accommodation?: Accommodation;
  meals: Meal[];
  transportation?: Transportation;
}

// Activity
export interface Activity {
  id: string;
  name: string;
  description: string;
  startTime: string;
  endTime: string;
  location: string;
  type: 'sightseeing' | 'adventure' | 'cultural' | 'leisure' | 'dining';
  cost?: number;
  isOptional: boolean;
}

// Accommodation
export interface Accommodation {
  id: string;
  name: string;
  type: 'hotel' | 'resort' | 'hostel' | 'apartment' | 'guesthouse';
  rating: number;
  address: string;
  checkIn: string;
  checkOut: string;
  roomType: string;
  amenities: string[];
}

// Meal
export interface Meal {
  id: string;
  type: 'breakfast' | 'lunch' | 'dinner' | 'snack';
  name: string;
  restaurant?: string;
  cuisine: string;
  isIncluded: boolean;
  cost?: number;
}

// Transportation
export interface Transportation {
  id: string;
  type: 'flight' | 'train' | 'bus' | 'car' | 'boat';
  from: string;
  to: string;
  departureTime: string;
  arrivalTime: string;
  provider: string;
  bookingReference?: string;
}

// Booking Types
export interface Booking {
  id: string;
  packageId: string;
  consumerId: string;
  agentId: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  bookingDate: string;
  travelDate: string;
  numberOfTravelers: number;
  totalAmount: number;
  paidAmount: number;
  paymentStatus: 'pending' | 'partial' | 'paid' | 'refunded';
  specialRequests?: string;
  travelers: Traveler[];
  createdAt: string;
  updatedAt: string;
}

// Traveler
export interface Traveler {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  passportNumber?: string;
  nationality: string;
  dietaryRestrictions?: string[];
  medicalConditions?: string[];
}

// Notification Types
export interface Notification {
  id: string;
  recipientId: string;
  senderId?: string;
  type: 'booking_update' | 'payment_reminder' | 'trip_reminder' | 'system_alert' | 'agent_message';
  title: string;
  message: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  scheduledFor?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  expiresIn: number;
}

// Dashboard Stats
export interface AdminDashboardStats {
  totalAgents: number;
  activeAgents: number;
  totalBookings: number;
  totalRevenue: number;
  monthlyGrowth: number;
}

export interface AgentDashboardStats {
  totalPackages: number;
  activeBookings: number;
  monthlyRevenue: number;
  customerCount: number;
  averageRating: number;
}

export interface ConsumerDashboardStats {
  upcomingTrips: number;
  completedTrips: number;
  totalSpent: number;
  savedPackages: number;
}
