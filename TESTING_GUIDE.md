# 🧪 TravelEase Complete Testing Guide

This guide provides step-by-step instructions to test the complete TravelEase application flow across all three portals.

## 🚀 Prerequisites

1. **Docker Setup Complete**: All services running via Docker
2. **Services Accessible**:
   - API Server: http://localhost:8001
   - Admin Portal: http://localhost:8002
   - Agent Portal: http://localhost:8003
   - Consumer App: http://localhost:8004

## 🔐 Test Credentials

| Portal | Email | Password | Role |
|--------|-------|----------|------|
| Admin Portal | <EMAIL> | admin123 | Super Admin |
| Agent Portal | <EMAIL> | agent123 | Travel Agent |
| Consumer App | <EMAIL> | customer123 | Customer |

## 📋 Testing Checklist

### Phase 1: API Server Testing ✅

**Objective**: Verify backend API functionality

1. **Health Check**
   ```bash
   curl http://localhost:8001/health
   ```
   ✅ Expected: `{"status": "OK", "timestamp": "..."}`

2. **Authentication Test**
   ```bash
   curl -X POST http://localhost:8001/api/auth/login \
     -H 'Content-Type: application/json' \
     -d '{"email":"<EMAIL>","password":"admin123"}'
   ```
   ✅ Expected: JWT token response

3. **Agents Endpoint**
   ```bash
   curl http://localhost:8001/api/agents
   ```
   ✅ Expected: List of travel agents

### Phase 2: Super Admin Portal Testing ✅

**Objective**: Test admin management functionality

#### 2.1 Login Process
1. Open http://localhost:8002
2. Enter credentials: `<EMAIL>` / `admin123`
3. Click "Sign In"
   ✅ **Expected**: Redirect to dashboard

#### 2.2 Dashboard Verification
1. Verify dashboard loads with:
   - Total agents count
   - Active bookings chart
   - Revenue metrics
   - Recent activities
   ✅ **Expected**: All widgets display data

#### 2.3 Agent Management
1. Navigate to "Agents" section
2. **Add New Agent**:
   - Click "Add Agent"
   - Fill form:
     - Name: "Test Agent"
     - Email: "<EMAIL>"
     - Phone: "+1234567890"
     - Company: "Test Travel Co"
   - Click "Save"
   ✅ **Expected**: Agent added successfully

3. **Edit Agent**:
   - Click edit icon on any agent
   - Modify details
   - Save changes
   ✅ **Expected**: Changes saved

4. **View Agent Details**:
   - Click on agent name
   - Verify all information displays
   ✅ **Expected**: Complete agent profile shown

5. **Search/Filter**:
   - Use search bar to find agents
   - Test filter by status
   ✅ **Expected**: Results filter correctly

#### 2.4 Settings Management
1. Navigate to "Settings"
2. Update system configurations
3. Save changes
   ✅ **Expected**: Settings updated successfully

#### 2.5 Logout
1. Click logout button
2. Verify redirect to login page
   ✅ **Expected**: Logged out successfully

### Phase 3: Travel Agent Portal Testing ✅

**Objective**: Test complete agent workflow

#### 3.1 Login Process
1. Open http://localhost:8003
2. Enter credentials: `<EMAIL>` / `agent123`
3. Click "Sign In"
   ✅ **Expected**: Redirect to agent dashboard

#### 3.2 Dashboard Verification
1. Verify dashboard shows:
   - Booking statistics
   - Revenue charts
   - Recent bookings
   - Package performance
   ✅ **Expected**: All metrics display correctly

#### 3.3 Package Management
1. Navigate to "Packages"
2. **Create New Package**:
   - Click "Add Package"
   - Fill details:
     - Name: "Dubai Desert Safari"
     - Description: "Exciting desert adventure"
     - Duration: "6 hours"
     - Price: "$150"
     - Destination: "Dubai"
   - Add highlights
   - Upload images (test with sample images)
   - Create itinerary items
   - Set availability dates
   - Click "Save"
   ✅ **Expected**: Package created successfully

3. **Edit Package**:
   - Click edit on existing package
   - Modify details
   - Save changes
   ✅ **Expected**: Package updated

4. **Package Gallery**:
   - Test image upload
   - Verify image display
   ✅ **Expected**: Images upload and display correctly

#### 3.4 Customer Management
1. Navigate to "Customers"
2. **Add New Customer**:
   - Click "Add Customer"
   - Fill profile:
     - Name: "John Doe"
     - Email: "<EMAIL>"
     - Phone: "+1987654321"
     - Address details
   - Save customer
   ✅ **Expected**: Customer profile created

3. **View Customer Details**:
   - Click on customer name
   - Verify booking history
   - Check contact information
   ✅ **Expected**: Complete customer profile shown

#### 3.5 Booking Management
1. Navigate to "Bookings"
2. **Create New Booking**:
   - Click "New Booking"
   - Select customer
   - Select package
   - Choose dates
   - Add travelers
   - Set payment details
   - Save booking
   ✅ **Expected**: Booking created successfully

3. **Update Booking Status**:
   - Find a booking
   - Change status (Pending → Confirmed)
   - Save changes
   ✅ **Expected**: Status updated

4. **Booking Details**:
   - Click on booking ID
   - Verify all information
   - Check payment status
   ✅ **Expected**: Complete booking details shown

#### 3.6 Itinerary Builder
1. Navigate to "Itinerary Builder"
2. **Create Detailed Itinerary**:
   - Select package
   - Add Day 1 activities:
     - Time: "09:00 AM"
     - Activity: "Hotel Pickup"
     - Location: "Dubai Marina"
   - Add Day 2 activities
   - Include meals and accommodations
   - Save itinerary
   ✅ **Expected**: Itinerary created successfully

#### 3.7 Notifications
1. Navigate to "Notifications"
2. **Send Customer Notification**:
   - Select customer
   - Choose notification type
   - Write message: "Your trip is confirmed!"
   - Send notification
   ✅ **Expected**: Notification sent successfully

### Phase 4: Consumer Mobile App Testing ✅

**Objective**: Test customer experience

#### 4.1 Login Process
1. Open http://localhost:8004
2. Enter credentials: `<EMAIL>` / `customer123`
3. Click "Sign In"
   ✅ **Expected**: Redirect to trip dashboard

#### 4.2 Trip Dashboard
1. Verify trip cards display:
   - Trip images
   - Trip titles
   - Status badges (Today, Tomorrow, Upcoming, Completed)
   - Pickup times
   - Action buttons
   ✅ **Expected**: All trips display correctly

2. **Search Functionality**:
   - Use search bar
   - Search for "Desert"
   - Verify results filter
   ✅ **Expected**: Search works correctly

3. **Trip Filtering**:
   - Test filter chips (All, Upcoming, Completed)
   - Verify trips filter by status
   ✅ **Expected**: Filters work correctly

#### 4.3 Trip Details
1. Click on any trip card
2. **Image Gallery**:
   - Swipe through images
   - Verify all images load
   ✅ **Expected**: Gallery works smoothly

3. **Highlights Tab**:
   - Verify highlights list
   - Check pickup information
   - Verify chauffeur details
   ✅ **Expected**: All information displays correctly

4. **Itinerary Tab**:
   - Switch to Itinerary tab
   - Verify timeline view
   - Check activity icons
   - Verify times and descriptions
   ✅ **Expected**: Timeline displays correctly

5. **Action Buttons**:
   - Test chat button
   - Test "Enjoy the trip" button
   ✅ **Expected**: Buttons respond correctly

#### 4.4 Profile Management
1. Navigate to Profile tab
2. **View Profile**:
   - Verify user information
   - Check contact details
   ✅ **Expected**: Profile displays correctly

3. **Settings Access**:
   - Test settings sections
   - Verify navigation
   ✅ **Expected**: Settings accessible

#### 4.5 Notifications
1. Navigate to Notifications tab
2. **View Notifications**:
   - Verify notification list
   - Check unread badges
   - Verify notification types
   ✅ **Expected**: Notifications display correctly

3. **Mark as Read**:
   - Tap on unread notification
   - Verify it marks as read
   ✅ **Expected**: Read status updates

4. **Mark All Read**:
   - Use "Mark all read" button
   - Verify all notifications marked
   ✅ **Expected**: All marked as read

### Phase 5: Cross-Application Integration Testing ✅

**Objective**: Verify data flow between applications

#### 5.1 End-to-End Workflow
1. **Agent Portal**: Create new package "City Tour"
2. **Agent Portal**: Create customer "Jane Smith"
3. **Agent Portal**: Create booking for Jane with City Tour
4. **Agent Portal**: Send notification to Jane
5. **Consumer App**: Login as Jane (create test credentials)
6. **Consumer App**: Verify booking appears in dashboard
7. **Consumer App**: Verify notification received
8. **Consumer App**: Check trip details match package
   ✅ **Expected**: Complete data flow works

#### 5.2 Real-time Updates
1. **Agent Portal**: Update booking status
2. **Consumer App**: Refresh and verify status change
3. **Agent Portal**: Send new notification
4. **Consumer App**: Verify notification appears
   ✅ **Expected**: Updates reflect in real-time

### Phase 6: Error Handling Testing ✅

#### 6.1 Authentication Errors
1. **Invalid Credentials**:
   - Try wrong password on all portals
   ✅ **Expected**: Error message displayed

2. **Empty Fields**:
   - Submit login with empty fields
   ✅ **Expected**: Validation errors shown

#### 6.2 Form Validation
1. **Required Fields**:
   - Try submitting forms with missing data
   ✅ **Expected**: Validation messages appear

2. **Invalid Formats**:
   - Enter invalid email formats
   - Enter invalid phone numbers
   ✅ **Expected**: Format validation works

#### 6.3 Network Errors
1. **API Unavailable**:
   - Stop API server: `docker-compose stop api-server`
   - Try using frontends
   ✅ **Expected**: Error messages displayed
   - Restart: `docker-compose start api-server`

### Phase 7: Performance Testing ✅

#### 7.1 Load Times
1. Measure page load times for each portal
2. Test with browser dev tools
   ✅ **Expected**: Pages load within 3 seconds

#### 7.2 Image Handling
1. Upload large images in Agent Portal
2. Verify compression and display
   ✅ **Expected**: Images handle efficiently

#### 7.3 Multiple Sessions
1. Open multiple browser tabs
2. Test concurrent usage
   ✅ **Expected**: No performance degradation

### Phase 8: Mobile Responsiveness ✅

#### 8.1 Consumer App Responsiveness
1. Test on different screen sizes:
   - Mobile (375px)
   - Tablet (768px)
   - Desktop (1200px)
   ✅ **Expected**: Responsive design works

2. **Touch Interactions**:
   - Test tap targets
   - Test swipe gestures
   ✅ **Expected**: Touch-friendly interface

### Phase 9: Browser Compatibility ✅

Test on multiple browsers:
- ✅ Chrome
- ✅ Firefox  
- ✅ Safari
- ✅ Edge

## 🎯 Success Criteria

All tests should pass with:
- ✅ No console errors
- ✅ Proper error handling
- ✅ Data consistency across apps
- ✅ Responsive design
- ✅ Fast load times
- ✅ Intuitive user experience

## 🐛 Troubleshooting

If tests fail:
1. Check service logs: `docker-compose logs -f`
2. Verify all services running: `docker-compose ps`
3. Check network connectivity
4. Restart services: `docker-compose restart`
5. Clear browser cache

## 📊 Test Report Template

```
TravelEase Testing Report
========================
Date: [DATE]
Tester: [NAME]
Environment: Docker Local

✅ API Server: PASS/FAIL
✅ Admin Portal: PASS/FAIL  
✅ Agent Portal: PASS/FAIL
✅ Consumer App: PASS/FAIL
✅ Integration: PASS/FAIL
✅ Performance: PASS/FAIL
✅ Mobile: PASS/FAIL
✅ Browser Compatibility: PASS/FAIL

Issues Found:
- [List any issues]

Overall Status: PASS/FAIL
```

Happy Testing! 🚀
