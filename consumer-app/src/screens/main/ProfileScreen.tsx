import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Avatar, List, Divider } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';

import { useAuth } from '@/store/authStore';
import { colors, spacing, typography, shadows } from '@/constants/theme';

const ProfileScreen: React.FC = () => {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: logout },
      ]
    );
  };

  const profileSections = [
    {
      title: 'Account',
      items: [
        {
          title: 'Edit Profile',
          description: 'Update your personal information',
          icon: 'person-outline',
          onPress: () => console.log('Edit Profile'),
        },
        {
          title: 'Notifications',
          description: 'Manage your notification preferences',
          icon: 'notifications-outline',
          onPress: () => console.log('Notifications'),
        },
        {
          title: 'Privacy & Security',
          description: 'Manage your privacy settings',
          icon: 'shield-outline',
          onPress: () => console.log('Privacy'),
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          title: 'Help Center',
          description: 'Get help and support',
          icon: 'help-circle-outline',
          onPress: () => console.log('Help'),
        },
        {
          title: 'Contact Us',
          description: 'Get in touch with our team',
          icon: 'mail-outline',
          onPress: () => console.log('Contact'),
        },
        {
          title: 'Terms & Conditions',
          description: 'Read our terms and conditions',
          icon: 'document-text-outline',
          onPress: () => console.log('Terms'),
        },
      ],
    },
    {
      title: 'App',
      items: [
        {
          title: 'About',
          description: 'Learn more about TravelEase',
          icon: 'information-circle-outline',
          onPress: () => console.log('About'),
        },
        {
          title: 'Rate App',
          description: 'Rate us on the app store',
          icon: 'star-outline',
          onPress: () => console.log('Rate'),
        },
        {
          title: 'Share App',
          description: 'Share TravelEase with friends',
          icon: 'share-outline',
          onPress: () => console.log('Share'),
        },
      ],
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.profileInfo}>
            <Avatar.Text
              size={80}
              label={user?.firstName?.charAt(0) || 'U'}
              style={styles.avatar}
            />
            <View style={styles.userInfo}>
              <Text style={styles.userName}>
                {user?.firstName} {user?.lastName}
              </Text>
              <Text style={styles.userEmail}>{user?.email}</Text>
              {user?.phone && (
                <Text style={styles.userPhone}>{user.phone}</Text>
              )}
            </View>
          </View>
          
          <TouchableOpacity style={styles.editButton}>
            <Ionicons name="pencil-outline" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Profile Sections */}
        {profileSections.map((section, sectionIndex) => (
          <View key={section.title} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <View style={styles.sectionContent}>
              {section.items.map((item, itemIndex) => (
                <View key={item.title}>
                  <List.Item
                    title={item.title}
                    description={item.description}
                    left={(props) => (
                      <List.Icon
                        {...props}
                        icon={item.icon}
                        color={colors.text.secondary}
                      />
                    )}
                    right={(props) => (
                      <List.Icon
                        {...props}
                        icon="chevron-right"
                        color={colors.text.secondary}
                      />
                    )}
                    onPress={item.onPress}
                    titleStyle={styles.listItemTitle}
                    descriptionStyle={styles.listItemDescription}
                    style={styles.listItem}
                  />
                  {itemIndex < section.items.length - 1 && (
                    <Divider style={styles.divider} />
                  )}
                </View>
              ))}
            </View>
          </View>
        ))}

        {/* Logout Button */}
        <View style={styles.logoutSection}>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Ionicons name="log-out-outline" size={24} color={colors.error} />
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>

        {/* App Version */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>Version 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: spacing.lg,
    backgroundColor: colors.white,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    backgroundColor: colors.primary,
    marginRight: spacing.md,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  userEmail: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  userPhone: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
  },
  editButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.text.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    backgroundColor: colors.surface,
  },
  sectionContent: {
    backgroundColor: colors.white,
    ...shadows.sm,
  },
  listItem: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
  listItemTitle: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.medium,
    color: colors.text.primary,
  },
  listItemDescription: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
  },
  divider: {
    marginLeft: spacing.lg + 40, // Align with text
  },
  logoutSection: {
    padding: spacing.lg,
    marginTop: spacing.lg,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    paddingVertical: spacing.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.error,
    ...shadows.sm,
  },
  logoutText: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.error,
    marginLeft: spacing.sm,
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  versionText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
  },
});

export default ProfileScreen;
