export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  dateOfBirth?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    country: string;
    zipCode: string;
  };
  preferences?: {
    currency: string;
    language: string;
    notifications: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Trip {
  id: string;
  title: string;
  description: string;
  destination: string;
  startDate: string;
  endDate: string;
  status: 'upcoming' | 'today' | 'tomorrow' | 'completed' | 'cancelled';
  image: string;
  packageId: string;
  bookingId: string;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  participants: number;
  pickupLocation?: string;
  pickupTime?: string;
  dropoffLocation?: string;
  dropoffTime?: string;
  agentId: string;
  agent: {
    id: string;
    name: string;
    phone: string;
    email: string;
    avatar?: string;
  };
  itinerary: ItineraryItem[];
  documents: TripDocument[];
  createdAt: string;
  updatedAt: string;
}

export interface ItineraryItem {
  id: string;
  day: number;
  time: string;
  title: string;
  description: string;
  location: string;
  type: 'activity' | 'meal' | 'transport' | 'accommodation' | 'free_time';
  duration?: string;
  notes?: string;
  images?: string[];
}

export interface TripDocument {
  id: string;
  name: string;
  type: 'ticket' | 'voucher' | 'passport' | 'visa' | 'insurance' | 'other';
  url: string;
  uploadedAt: string;
}

export interface Package {
  id: string;
  title: string;
  description: string;
  destination: string;
  duration: number;
  price: number;
  originalPrice?: number;
  images: string[];
  highlights: string[];
  inclusions: string[];
  exclusions: string[];
  itinerary: PackageItineraryItem[];
  rating: number;
  reviewCount: number;
  difficulty: 'easy' | 'moderate' | 'challenging';
  groupSize: {
    min: number;
    max: number;
  };
  ageRestriction?: {
    min: number;
    max: number;
  };
  availableDates: string[];
  tags: string[];
  agentId: string;
  agent: {
    id: string;
    name: string;
    company: string;
    rating: number;
    avatar?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface PackageItineraryItem {
  day: number;
  title: string;
  description: string;
  activities: string[];
  meals: string[];
  accommodation?: string;
}

export interface Booking {
  id: string;
  packageId: string;
  userId: string;
  startDate: string;
  participants: number;
  totalAmount: number;
  paidAmount: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  paymentStatus: 'pending' | 'partial' | 'paid' | 'refunded';
  specialRequests?: string;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  travelers: Traveler[];
  createdAt: string;
  updatedAt: string;
}

export interface Traveler {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  passportNumber?: string;
  passportExpiry?: string;
  nationality: string;
  dietaryRestrictions?: string[];
  medicalConditions?: string[];
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'trip_reminder' | 'trip_update' | 'payment_due' | 'booking_confirmed' | 'weather_alert';
  read: boolean;
  data?: any;
  createdAt: string;
}

export interface ChatMessage {
  id: string;
  tripId: string;
  senderId: string;
  senderType: 'user' | 'agent';
  message: string;
  type: 'text' | 'image' | 'document' | 'location';
  attachments?: string[];
  createdAt: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  TripDetails: { tripId: string };
  PackageDetails: { packageId: string };
  BookingForm: { packageId: string };
  Payment: { bookingId: string };
  Chat: { tripId: string };
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Trips: undefined;
  Profile: undefined;
  Notifications: undefined;
};
