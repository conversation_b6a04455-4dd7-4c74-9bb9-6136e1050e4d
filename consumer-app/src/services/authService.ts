import { apiService } from './api';
import { User, ApiResponse } from '@/types';

interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

interface ForgotPasswordData {
  email: string;
}

interface ResetPasswordData {
  token: string;
  password: string;
}

class AuthService {
  async login(email: string, password: string): Promise<ApiResponse<LoginResponse>> {
    return apiService.post<LoginResponse>('/auth/login', {
      email,
      password,
    });
  }

  async register(userData: RegisterData): Promise<ApiResponse<LoginResponse>> {
    return apiService.post<LoginResponse>('/auth/register', userData);
  }

  async logout(): Promise<ApiResponse<void>> {
    return apiService.post<void>('/auth/logout');
  }

  async forgotPassword(data: ForgotPasswordData): Promise<ApiResponse<{ message: string }>> {
    return apiService.post<{ message: string }>('/auth/forgot-password', data);
  }

  async resetPassword(data: ResetPasswordData): Promise<ApiResponse<{ message: string }>> {
    return apiService.post<{ message: string }>('/auth/reset-password', data);
  }

  async verifyToken(token: string): Promise<boolean> {
    try {
      const response = await apiService.post<{ valid: boolean }>('/auth/verify-token', {
        token,
      });
      return response.success && response.data.valid;
    } catch (error) {
      return false;
    }
  }

  async refreshToken(refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> {
    return apiService.post<{ token: string; refreshToken: string }>('/auth/refresh-token', {
      refreshToken,
    });
  }

  async updateProfile(userData: Partial<User>): Promise<ApiResponse<User>> {
    return apiService.put<User>('/auth/profile', userData);
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse<{ message: string }>> {
    return apiService.post<{ message: string }>('/auth/change-password', {
      currentPassword,
      newPassword,
    });
  }

  async deleteAccount(): Promise<ApiResponse<{ message: string }>> {
    return apiService.delete<{ message: string }>('/auth/account');
  }

  // Social login methods
  async loginWithGoogle(idToken: string): Promise<ApiResponse<LoginResponse>> {
    return apiService.post<LoginResponse>('/auth/google', {
      idToken,
    });
  }

  async loginWithFacebook(accessToken: string): Promise<ApiResponse<LoginResponse>> {
    return apiService.post<LoginResponse>('/auth/facebook', {
      accessToken,
    });
  }

  async loginWithApple(identityToken: string, authorizationCode: string): Promise<ApiResponse<LoginResponse>> {
    return apiService.post<LoginResponse>('/auth/apple', {
      identityToken,
      authorizationCode,
    });
  }
}

export const authService = new AuthService();
