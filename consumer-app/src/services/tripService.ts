import { apiService } from './api';
import { Trip, ApiResponse, PaginatedResponse } from '@/types';

class TripService {
  async getTrips(page = 1, limit = 10): Promise<ApiResponse<PaginatedResponse<Trip>>> {
    return apiService.get<PaginatedResponse<Trip>>(`/trips?page=${page}&limit=${limit}`);
  }

  async getTripById(tripId: string): Promise<ApiResponse<Trip>> {
    return apiService.get<Trip>(`/trips/${tripId}`);
  }

  async getUpcomingTrips(): Promise<ApiResponse<Trip[]>> {
    return apiService.get<Trip[]>('/trips/upcoming');
  }

  async getTripsByStatus(status: string): Promise<ApiResponse<Trip[]>> {
    return apiService.get<Trip[]>(`/trips?status=${status}`);
  }

  async searchTrips(query: string): Promise<ApiResponse<Trip[]>> {
    return apiService.get<Trip[]>(`/trips/search?q=${encodeURIComponent(query)}`);
  }

  async updateTripStatus(tripId: string, status: string): Promise<ApiResponse<Trip>> {
    return apiService.patch<Trip>(`/trips/${tripId}/status`, { status });
  }

  async addTripReview(tripId: string, rating: number, comment: string): Promise<ApiResponse<{ message: string }>> {
    return apiService.post<{ message: string }>(`/trips/${tripId}/review`, {
      rating,
      comment,
    });
  }

  async uploadTripDocument(tripId: string, file: any, type: string): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return apiService.post<{ url: string }>(`/trips/${tripId}/documents`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async getTripDocuments(tripId: string): Promise<ApiResponse<any[]>> {
    return apiService.get<any[]>(`/trips/${tripId}/documents`);
  }

  async shareTripItinerary(tripId: string, email: string): Promise<ApiResponse<{ message: string }>> {
    return apiService.post<{ message: string }>(`/trips/${tripId}/share`, { email });
  }
}

export const tripService = new TripService();
