import { apiService } from './api';
import { Notification, ApiResponse, PaginatedResponse } from '@/types';

class NotificationService {
  async getNotifications(page = 1, limit = 20): Promise<ApiResponse<PaginatedResponse<Notification>>> {
    return apiService.get<PaginatedResponse<Notification>>(`/notifications?page=${page}&limit=${limit}`);
  }

  async markAsRead(notificationId: string): Promise<ApiResponse<{ message: string }>> {
    return apiService.patch<{ message: string }>(`/notifications/${notificationId}/read`);
  }

  async markAllAsRead(): Promise<ApiResponse<{ message: string }>> {
    return apiService.patch<{ message: string }>('/notifications/read-all');
  }

  async deleteNotification(notificationId: string): Promise<ApiResponse<{ message: string }>> {
    return apiService.delete<{ message: string }>(`/notifications/${notificationId}`);
  }

  async getUnreadCount(): Promise<ApiResponse<{ count: number }>> {
    return apiService.get<{ count: number }>('/notifications/unread-count');
  }

  async updateNotificationSettings(settings: any): Promise<ApiResponse<{ message: string }>> {
    return apiService.put<{ message: string }>('/notifications/settings', settings);
  }

  async getNotificationSettings(): Promise<ApiResponse<any>> {
    return apiService.get<any>('/notifications/settings');
  }
}

export const notificationService = new NotificationService();
