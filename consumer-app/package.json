{"name": "travelease-mobile", "version": "1.0.0", "description": "TravelEase Mobile App for Consumers", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"expo": "~49.0.15", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "expo-linear-gradient": "~12.3.0", "expo-font": "~11.4.0", "expo-splash-screen": "~0.20.5", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-notifications": "~0.20.1", "expo-location": "~16.1.0", "expo-image-picker": "~14.3.2", "expo-secure-store": "~12.3.1", "expo-linking": "~5.0.2", "axios": "^1.4.0", "react-native-vector-icons": "^10.0.0", "@expo/vector-icons": "^13.0.0", "react-native-paper": "^5.10.1", "react-native-toast-message": "^2.1.6", "react-native-modal": "^13.0.1", "react-native-maps": "1.7.1", "react-native-calendars": "^1.1302.0", "react-native-image-viewing": "^0.2.2", "react-native-share": "^9.4.1", "react-native-webview": "13.2.2", "date-fns": "^2.30.0", "react-hook-form": "^7.45.4", "zustand": "^4.4.1", "react-native-web": "~0.19.6", "react-dom": "18.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.45.0", "eslint-config-expo": "^7.0.0", "jest": "^29.2.1", "jest-expo": "~49.0.0", "typescript": "^5.1.3"}, "keywords": ["react-native", "expo", "travel", "mobile", "booking"], "author": "TravelEase Team", "license": "MIT", "private": true}