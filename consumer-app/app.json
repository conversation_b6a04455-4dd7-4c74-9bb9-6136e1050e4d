{"expo": {"name": "TravelEase", "slug": "travelease-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#00BCD4"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.travelease.mobile"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#00BCD4"}, "package": "com.travelease.mobile", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-location", "expo-notifications", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your travel agent.", "cameraPermission": "The app accesses your camera to let you take photos during your trip."}]], "extra": {"eas": {"projectId": "your-project-id"}}}}