# Use Node.js 18 Alpine
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install Expo CLI globally (specific version to avoid issues)
RUN npm install -g @expo/cli@0.10.16

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY . .

# Create .expo directory
RUN mkdir -p .expo

# Expose port for Expo web
EXPOSE 19006

# Set environment variables
ENV EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
ENV REACT_NATIVE_PACKAGER_HOSTNAME=0.0.0.0

# Start Expo in web mode
CMD ["npx", "expo", "start", "--web", "--port", "19006", "--host", "0.0.0.0"]
