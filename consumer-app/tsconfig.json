{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/navigation/*": ["./src/navigation/*"], "@/services/*": ["./src/services/*"], "@/store/*": ["./src/store/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/constants/*": ["./src/constants/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"]}