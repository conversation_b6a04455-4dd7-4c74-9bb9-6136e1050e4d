{"compilerOptions": {"target": "esnext", "lib": ["dom", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/navigation/*": ["./src/navigation/*"], "@/services/*": ["./src/services/*"], "@/store/*": ["./src/store/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/constants/*": ["./src/constants/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "extends": "expo/tsconfig.base"}