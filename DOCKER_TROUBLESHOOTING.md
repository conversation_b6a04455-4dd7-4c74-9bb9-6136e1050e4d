# 🐳 TravelEase Docker Troubleshooting Guide

This guide helps resolve common Docker build and runtime issues with TravelEase.

## 🚨 Common Docker Build Issues

### Issue 1: `npm ci` requires package-lock.json

**Error:**
```
npm error The `npm ci` command can only install with an existing package-lock.json
```

**Solution:**
```bash
# Run the Docker preparation script
./scripts/prepare-docker.sh

# Or manually generate package-lock.json files
cd api-server && npm install && cd ..
cd admin-portal && npm install && cd ..
cd agent-portal && npm install && cd ..
cd consumer-app && npm install && cd ..
```

### Issue 2: Docker version warning

**Error:**
```
WARN: the attribute `version` is obsolete
```

**Solution:**
This is just a warning and can be ignored. The docker-compose.yml has been updated to remove the version field.

### Issue 3: Build context issues

**Error:**
```
failed to compute cache key: "/shared": not found
```

**Solution:**
The Dockerfiles have been updated to remove problematic shared directory copying. This should no longer occur.

### Issue 4: Port conflicts

**Error:**
```
Port 8001 is already in use
```

**Solution:**
```bash
# Check what's using the port
lsof -i :8001

# Kill the process
kill -9 $(lsof -t -i:8001)

# Or use different ports in .env file
```

## 🔧 Docker Setup Solutions

### Complete Clean Setup

If you're having persistent issues, try a complete clean setup:

```bash
# 1. Stop all containers
docker-compose down -v

# 2. Remove all images
docker system prune -a

# 3. Prepare for Docker build
./scripts/prepare-docker.sh

# 4. Build and start fresh
./scripts/docker-setup.sh
```

### Manual Docker Build

If the automated script fails, try manual steps:

```bash
# 1. Ensure package-lock.json files exist
./scripts/prepare-docker.sh

# 2. Build images individually
docker-compose build api-server
docker-compose build admin-portal
docker-compose build agent-portal
docker-compose build consumer-app

# 3. Start services
docker-compose up -d
```

### Development vs Production

**For Development (with hot reload):**
```bash
./scripts/docker-dev.sh
```

**For Production:**
```bash
./scripts/docker-setup.sh
```

## 🐛 Runtime Issues

### Issue 1: Services not starting

**Check service status:**
```bash
docker-compose ps
```

**Check logs:**
```bash
docker-compose logs -f
docker-compose logs -f api-server
```

**Restart specific service:**
```bash
docker-compose restart api-server
```

### Issue 2: API connection issues

**Check API health:**
```bash
curl http://localhost:8001/health
```

**Check network connectivity:**
```bash
docker network ls
docker network inspect travelease_default
```

**Test inter-service communication:**
```bash
docker-compose exec admin-portal curl http://api-server:3000/health
```

### Issue 3: Environment variables not working

**Check environment files:**
```bash
# Verify .env file exists
ls -la .env

# Check environment variables in container
docker-compose exec api-server env | grep API
```

**Recreate .env file:**
```bash
cp .env.example .env
# Edit .env with your values
```

## 📊 Performance Issues

### Issue 1: Slow build times

**Use build cache:**
```bash
docker-compose build --parallel
```

**Clean build (if cache is corrupted):**
```bash
docker-compose build --no-cache
```

### Issue 2: High memory usage

**Check resource usage:**
```bash
docker stats
```

**Increase Docker memory limit:**
- Docker Desktop → Settings → Resources → Memory → 8GB+

### Issue 3: Disk space issues

**Clean up Docker:**
```bash
docker system prune -a
docker volume prune
```

## 🔍 Debugging Commands

### Container Inspection

```bash
# Enter running container
docker-compose exec api-server bash
docker-compose exec admin-portal sh

# Check container logs
docker-compose logs --tail=100 -f api-server

# Inspect container
docker inspect travelease_api-server_1
```

### Network Debugging

```bash
# List networks
docker network ls

# Inspect network
docker network inspect travelease_default

# Test connectivity
docker-compose exec admin-portal ping api-server
```

### Volume Debugging

```bash
# List volumes
docker volume ls

# Inspect volume
docker volume inspect travelease_api-server-uploads
```

## 🚀 Alternative Solutions

### If Docker continues to fail

**Option 1: Manual Setup**
```bash
# Use manual testing guide
./scripts/test-without-docker.sh

# Start services manually
npm run install:all
cd api-server && npm run dev &
cd admin-portal && npm start &
cd agent-portal && PORT=3002 npm start &
cd consumer-app && npm start &
```

**Option 2: Docker Compose without build**
```bash
# Pull pre-built images (if available)
docker-compose pull

# Or use development setup
./scripts/docker-dev.sh
```

## 📋 Verification Checklist

Before reporting issues, verify:

- [ ] Docker and Docker Compose are installed
- [ ] Node.js and npm are available
- [ ] package-lock.json files exist in all directories
- [ ] .env file exists and is configured
- [ ] Ports 8001-8006 are available
- [ ] Sufficient disk space (>10GB)
- [ ] Sufficient memory (>8GB)

## 🆘 Getting Help

### Collect Debug Information

```bash
# System information
docker version
docker-compose version
node --version
npm --version

# Project status
./scripts/test-without-docker.sh

# Docker status
docker-compose ps
docker-compose logs --tail=50
```

### Common Solutions Summary

1. **Missing package-lock.json**: Run `./scripts/prepare-docker.sh`
2. **Port conflicts**: Kill processes using ports 8001-8006
3. **Build failures**: Clean Docker cache and rebuild
4. **Runtime issues**: Check logs and restart services
5. **Network issues**: Verify Docker network configuration
6. **Performance issues**: Increase Docker resources

### Last Resort

If all else fails:

```bash
# Complete reset
docker system prune -a -f
rm -rf */node_modules
./scripts/prepare-docker.sh
./scripts/docker-setup.sh
```

## 📞 Support

For additional help:
1. Check the main [DOCKER_SETUP.md](DOCKER_SETUP.md)
2. Review [MANUAL_TESTING_GUIDE.md](MANUAL_TESTING_GUIDE.md)
3. Run comprehensive validation: `./scripts/test-without-docker.sh`
4. Collect debug information and create an issue

Remember: The manual setup always works as a fallback! 🎯
