# 🧪 TravelEase Manual Testing Guide (Without Docker)

This guide provides step-by-step instructions for testing TravelEase manually without Dock<PERSON>.

## 📋 Prerequisites

- **Node.js** (v16 or higher)
- **npm** (v8 or higher)
- **4 Terminal windows/tabs**

## 🚀 Quick Setup

### 1. Install Dependencies
```bash
# Install all dependencies at once
npm run install:all

# Or install individually:
cd api-server && npm install && cd ..
cd admin-portal && npm install && cd ..
cd agent-portal && npm install && cd ..
cd consumer-app && npm install && cd ..
```

### 2. Start All Services

**Terminal 1 - API Server (Port 8001):**
```bash
cd api-server
npm run dev
```
Wait for: `🚀 Server running on port 8001`

**Terminal 2 - Admin Portal (Port 3000):**
```bash
cd admin-portal
npm start
```
Wait for: `Local: http://localhost:3000`

**Terminal 3 - Agent Portal (Port 3002):**
```bash
cd agent-portal
PORT=3002 npm start
```
Wait for: `Local: http://localhost:3002`

**Terminal 4 - Consumer App (Port 19006):**
```bash
cd consumer-app
npm start
```
Wait for: `Metro waiting on exp://localhost:19000`
Then press `w` to open web version

## 🌐 Application URLs

- **API Server**: http://localhost:8001
- **Admin Portal**: http://localhost:3000
- **Agent Portal**: http://localhost:3002
- **Consumer App**: http://localhost:19006

## 🔐 Test Credentials

| Portal | Email | Password | Role |
|--------|-------|----------|------|
| Admin Portal | <EMAIL> | admin123 | Super Admin |
| Agent Portal | <EMAIL> | agent123 | Travel Agent |
| Consumer App | <EMAIL> | customer123 | Customer |

## 🧪 Complete Testing Flow

### Phase 1: API Server Testing ✅

1. **Health Check**
   ```bash
   curl http://localhost:8001/health
   ```
   ✅ Expected: `{"status": "OK"}`

2. **Authentication Test**
   ```bash
   curl -X POST http://localhost:8001/api/auth/login \
     -H 'Content-Type: application/json' \
     -d '{"email":"<EMAIL>","password":"admin123"}'
   ```
   ✅ Expected: JWT token response

### Phase 2: Super Admin Portal Testing ✅

1. **Login Process**
   - Open http://localhost:3000
   - Login: `<EMAIL>` / `admin123`
   - ✅ Should redirect to dashboard

2. **Dashboard Verification**
   - Verify charts and statistics load
   - Check agent count and metrics
   - ✅ All widgets should display data

3. **Agent Management**
   - Navigate to "Agents" section
   - Add new agent with all details
   - Edit existing agent
   - Search and filter agents
   - ✅ All CRUD operations should work

### Phase 3: Travel Agent Portal Testing ✅

1. **Login Process**
   - Open http://localhost:3002
   - Login: `<EMAIL>` / `agent123`
   - ✅ Should redirect to agent dashboard

2. **Package Management**
   - Create new travel package
   - Add package images
   - Build detailed itinerary
   - ✅ Package creation should work

3. **Customer Management**
   - Add new customer profile
   - View customer details
   - ✅ Customer management should work

4. **Booking Management**
   - Create booking for customer
   - Update booking status
   - ✅ Booking workflow should work

### Phase 4: Consumer Mobile App Testing ✅

1. **Login Process**
   - Open http://localhost:19006
   - Login: `<EMAIL>` / `customer123`
   - ✅ Should redirect to trip dashboard

2. **Trip Dashboard**
   - View trip cards with status badges
   - Test search functionality
   - Filter trips by status
   - ✅ Dashboard should display correctly

3. **Trip Details**
   - Click on trip card
   - View image gallery
   - Switch between Highlights/Itinerary tabs
   - ✅ Trip details should work

## 🔄 Integration Testing

### End-to-End Workflow
1. **Admin Portal**: Create new agent
2. **Agent Portal**: Login with new agent
3. **Agent Portal**: Create package and customer
4. **Agent Portal**: Create booking for customer
5. **Consumer App**: Login as customer
6. **Consumer App**: Verify booking appears
7. ✅ Complete data flow should work

## 🐛 Troubleshooting

### Common Issues

**Port Conflicts:**
```bash
# Check what's using a port
lsof -i :3000
lsof -i :8001

# Kill process
kill -9 $(lsof -t -i:3000)
```

**API Connection Issues:**
- Verify API server is running on port 8001
- Check environment variables in `.env` files
- Ensure CORS is configured correctly

**Build Errors:**
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

**React App Issues:**
```bash
# Clear React cache
rm -rf node_modules/.cache
npm start
```

## 📊 Success Criteria

### ✅ All Tests Should Pass:
- [ ] API server responds to health check
- [ ] Admin portal loads and login works
- [ ] Agent portal loads and login works
- [ ] Consumer app loads and login works
- [ ] Agent creation in admin portal works
- [ ] Package creation in agent portal works
- [ ] Customer creation in agent portal works
- [ ] Booking creation in agent portal works
- [ ] Trip display in consumer app works
- [ ] Cross-application data flow works

### 🎯 Performance Expectations:
- Page load times < 3 seconds
- API responses < 1 second
- Smooth navigation between pages
- No console errors

## 📝 Test Report Template

```
TravelEase Manual Testing Report
===============================
Date: [DATE]
Tester: [NAME]
Environment: Local Development

API Server (Port 8001): ✅ PASS / ❌ FAIL
Admin Portal (Port 3000): ✅ PASS / ❌ FAIL
Agent Portal (Port 3002): ✅ PASS / ❌ FAIL
Consumer App (Port 19006): ✅ PASS / ❌ FAIL

Integration Flow: ✅ PASS / ❌ FAIL
Performance: ✅ PASS / ❌ FAIL

Issues Found:
- [List any issues]

Overall Status: ✅ PASS / ❌ FAIL
```

## 🚀 Next Steps

After successful manual testing:

1. **Install Docker** for containerized testing
2. **Run Docker setup**: `./scripts/docker-setup.sh`
3. **Production deployment** preparation
4. **User acceptance testing**

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section
2. Verify all services are running
3. Check browser console for errors
4. Review API server logs
5. Ensure all dependencies are installed

Happy Testing! 🎉
