{"name": "travelease-admin-portal", "version": "1.0.0", "description": "Super Admin Portal for TravelEase", "private": true, "dependencies": {"@types/node": "^20.4.5", "@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^3.3.2", "axios": "^1.4.0", "recharts": "^2.7.2", "lucide-react": "^0.263.1", "react-hot-toast": "^2.4.1", "clsx": "^2.0.0", "ajv": "6.12.6", "ajv-keywords": "3.5.2", "ajv-formats": "1.6.1"}, "devDependencies": {"tailwindcss": "^3.3.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.27", "@tailwindcss/forms": "^0.5.4", "@types/jest": "^29.5.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3"}, "overrides": {"ajv": "6.12.6", "ajv-keywords": "3.5.2", "ajv-formats": "1.6.1"}, "resolutions": {"ajv": "6.12.6", "ajv-keywords": "3.5.2", "ajv-formats": "1.6.1"}, "scripts": {"start": "react-scripts start", "build": "DISABLE_ESLINT_PLUGIN=true TSC_COMPILE_ON_ERROR=true react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}