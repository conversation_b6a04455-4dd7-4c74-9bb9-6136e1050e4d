import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Search, 
  Filter, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Eye,
  UserCheck,
  UserX
} from 'lucide-react';
import { TravelAgent } from '../../../shared/types';
import { agentService, AgentFilters } from '../services/agentService';
import { PageLoading } from '../components/LoadingSpinner';
import { formatCurrency, formatDate, getStatusColor } from '../utils';
import toast from 'react-hot-toast';

export const Agents: React.FC = () => {
  const [agents, setAgents] = useState<TravelAgent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadAgents();
  }, [currentPage, statusFilter]);

  const loadAgents = async () => {
    try {
      setIsLoading(true);
      const filters: AgentFilters = {
        page: currentPage,
        limit: 10,
        search: searchTerm || undefined,
        status: statusFilter === 'all' ? undefined : statusFilter,
      };
      
      const response = await agentService.getAgents(filters);
      setAgents(response.agents);
      setTotalPages(response.pagination.pages);
    } catch (error: any) {
      toast.error(error.message || 'Failed to load agents');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    loadAgents();
  };

  const handleToggleStatus = async (agentId: string, currentStatus: boolean) => {
    try {
      await agentService.toggleAgentStatus(agentId, !currentStatus);
      toast.success(`Agent ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
      loadAgents();
    } catch (error: any) {
      toast.error(error.message || 'Failed to update agent status');
    }
  };

  const handleDeleteAgent = async (agentId: string) => {
    if (!window.confirm('Are you sure you want to delete this agent?')) {
      return;
    }

    try {
      await agentService.deleteAgent(agentId);
      toast.success('Agent deleted successfully');
      loadAgents();
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete agent');
    }
  };

  if (isLoading && agents.length === 0) {
    return <PageLoading message="Loading agents..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Travel Agents</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage travel agents and their accounts
          </p>
        </div>
        <button className="btn-primary">
          <Plus className="w-4 h-4 mr-2" />
          Add Agent
        </button>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-content">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search agents..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                className="input"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
              <button onClick={handleSearch} className="btn-primary">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Agents Table */}
      <div className="card">
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">Agent</th>
                <th className="table-header-cell">Agency</th>
                <th className="table-header-cell">Specializations</th>
                <th className="table-header-cell">Bookings</th>
                <th className="table-header-cell">Revenue</th>
                <th className="table-header-cell">Rating</th>
                <th className="table-header-cell">Status</th>
                <th className="table-header-cell">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {agents.map((agent) => (
                <tr key={agent.id}>
                  <td className="table-cell">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center">
                          <span className="text-sm font-medium text-white">
                            {agent.firstName.charAt(0)}{agent.lastName.charAt(0)}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {agent.firstName} {agent.lastName}
                        </div>
                        <div className="text-sm text-gray-500">{agent.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="table-cell">
                    <div className="text-sm text-gray-900">{(agent as any).agencyName}</div>
                    <div className="text-sm text-gray-500">{(agent as any).licenseNumber}</div>
                  </td>
                  <td className="table-cell">
                    <div className="flex flex-wrap gap-1">
                      {(agent as any).specializations?.slice(0, 2).map((spec: string, index: number) => (
                        <span key={index} className="badge badge-info">
                          {spec}
                        </span>
                      ))}
                      {(agent as any).specializations?.length > 2 && (
                        <span className="text-xs text-gray-500">
                          +{(agent as any).specializations.length - 2} more
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="table-cell">
                    <div className="text-sm text-gray-900">{(agent as any).totalBookings || 0}</div>
                  </td>
                  <td className="table-cell">
                    <div className="text-sm text-gray-900">
                      {formatCurrency((agent as any).revenue || 0)}
                    </div>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center">
                      <div className="text-sm text-gray-900">{(agent as any).rating || 0}</div>
                      <svg className="ml-1 w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className={`badge ${(agent as any).isActive ? 'badge-success' : 'badge-danger'}`}>
                      {(agent as any).isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-2">
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-gray-600">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleToggleStatus(agent.id, (agent as any).isActive)}
                        className="p-1 text-gray-400 hover:text-gray-600"
                      >
                        {(agent as any).isActive ? <UserX className="w-4 h-4" /> : <UserCheck className="w-4 h-4" />}
                      </button>
                      <button
                        onClick={() => handleDeleteAgent(agent.id)}
                        className="p-1 text-red-400 hover:text-red-600"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="card-footer">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="btn-outline disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="btn-outline disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
