import { TravelAgent, AdminDashboardStats } from '../../../shared/types';
import { apiService } from './api';

export interface AgentListResponse {
  agents: TravelAgent[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface AgentFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'active' | 'inactive';
}

class AgentService {
  async getAgents(filters: AgentFilters = {}): Promise<AgentListResponse> {
    const params = new URLSearchParams();
    
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.status) params.append('status', filters.status);

    const response = await apiService.get<AgentListResponse>(`/agents?${params.toString()}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch agents');
    }
    
    return response.data;
  }

  async getAgent(id: string): Promise<TravelAgent> {
    const response = await apiService.get<TravelAgent>(`/agents/${id}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch agent');
    }
    
    return response.data;
  }

  async createAgent(agentData: Partial<TravelAgent>): Promise<TravelAgent> {
    const response = await apiService.post<TravelAgent>('/agents', agentData);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to create agent');
    }
    
    return response.data;
  }

  async updateAgent(id: string, agentData: Partial<TravelAgent>): Promise<TravelAgent> {
    const response = await apiService.put<TravelAgent>(`/agents/${id}`, agentData);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update agent');
    }
    
    return response.data;
  }

  async deleteAgent(id: string): Promise<void> {
    const response = await apiService.delete(`/agents/${id}`);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to delete agent');
    }
  }

  async getAgentStats(): Promise<AdminDashboardStats> {
    const response = await apiService.get<AdminDashboardStats>('/agents/stats/overview');
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch agent statistics');
    }
    
    return response.data;
  }

  async toggleAgentStatus(id: string, isActive: boolean): Promise<TravelAgent> {
    const response = await apiService.put<TravelAgent>(`/agents/${id}`, { isActive });
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update agent status');
    }
    
    return response.data;
  }

  async searchAgents(query: string): Promise<TravelAgent[]> {
    const response = await apiService.get<AgentListResponse>(`/agents?search=${encodeURIComponent(query)}&limit=50`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to search agents');
    }
    
    return response.data.agents;
  }
}

export const agentService = new AgentService();
