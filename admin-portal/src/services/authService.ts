import { LoginRequest, LoginResponse, User } from '../../../shared/types';
import { apiService } from './api';

class AuthService {
  async login(email: string, password: string): Promise<LoginResponse> {
    const loginData: LoginRequest = { email, password };
    const response = await apiService.post<LoginResponse>('/auth/login', loginData);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Login failed');
    }
    
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      // Even if logout fails on server, we should clear local storage
      console.error('Logout error:', error);
    }
  }

  async verifyToken(token: string): Promise<User> {
    // Make a request to verify the token is still valid
    const response = await apiService.get<User>('/users/profile');
    
    if (!response.success || !response.data) {
      throw new Error('Token verification failed');
    }
    
    return response.data;
  }

  async refreshToken(): Promise<LoginResponse> {
    const token = localStorage.getItem('travelease_auth_token');
    
    if (!token) {
      throw new Error('No token to refresh');
    }

    const response = await apiService.post<LoginResponse>('/auth/refresh', { token });
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Token refresh failed');
    }
    
    return response.data;
  }

  getCurrentUser(): User | null {
    try {
      const userData = localStorage.getItem('travelease_user_data');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  }

  getToken(): string | null {
    return localStorage.getItem('travelease_auth_token');
  }

  isAuthenticated(): boolean {
    const token = this.getToken();
    const user = this.getCurrentUser();
    return !!(token && user);
  }

  hasRole(role: string): boolean {
    const user = this.getCurrentUser();
    return user?.role === role;
  }

  isSuperAdmin(): boolean {
    return this.hasRole('super_admin');
  }
}

export const authService = new AuthService();
