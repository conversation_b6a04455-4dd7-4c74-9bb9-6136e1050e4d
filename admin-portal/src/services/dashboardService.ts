import { apiService } from './api';

export interface DashboardOverview {
  totalAgents: number;
  activeAgents: number;
  totalConsumers: number;
  totalPackages: number;
  totalBookings: number;
  confirmedBookings: number;
  pendingBookings: number;
  totalRevenue: number;
  paidRevenue: number;
  pendingRevenue: number;
  monthlyGrowth: number;
}

export interface RecentActivity {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  status: string;
  amount?: number;
}

export interface TopAgent {
  id: string;
  name: string;
  agencyName: string;
  bookings: number;
  revenue: number;
  rating: number;
}

export interface ChartData {
  monthlyBookings: Array<{
    month: string;
    bookings: number;
    revenue: number;
  }>;
  bookingsByStatus: Array<{
    status: string;
    count: number;
  }>;
}

export interface AdminDashboardData {
  overview: DashboardOverview;
  recentActivities: RecentActivity[];
  topAgents: TopAgent[];
  chartData: ChartData;
}

class DashboardService {
  async getAdminDashboard(): Promise<AdminDashboardData> {
    const response = await apiService.get<AdminDashboardData>('/dashboard/admin');
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch dashboard data');
    }
    
    return response.data;
  }

  async getSystemHealth(): Promise<any> {
    try {
      const response = await apiService.get('/health');
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch system health');
    }
  }

  async exportData(type: 'agents' | 'bookings' | 'revenue', format: 'csv' | 'xlsx' = 'csv'): Promise<Blob> {
    try {
      const response = await apiService.getApiInstance().get(`/dashboard/export/${type}`, {
        params: { format },
        responseType: 'blob',
      });
      
      return response.data;
    } catch (error) {
      throw new Error(`Failed to export ${type} data`);
    }
  }

  async getAnalytics(period: 'week' | 'month' | 'quarter' | 'year' = 'month'): Promise<any> {
    const response = await apiService.get(`/dashboard/analytics?period=${period}`);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch analytics data');
    }
    
    return response.data;
  }
}

export const dashboardService = new DashboardService();
