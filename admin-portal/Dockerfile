# Use Node.js 18 Alpine for development server
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY . .

# Expose port
EXPOSE 3000

# Set environment variables for development
ENV DISABLE_ESLINT_PLUGIN=true
ENV TSC_COMPILE_ON_ERROR=true
ENV GENERATE_SOURCEMAP=false
ENV SKIP_PREFLIGHT_CHECK=true
ENV REACT_APP_API_URL=http://localhost:8001
ENV WDS_SOCKET_HOST=localhost
ENV WDS_SOCKET_PORT=8002

# Start development server
CMD ["npm", "start"]
