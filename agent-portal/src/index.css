@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    
  }
  
  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 h-10 py-2 px-4;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 h-10 py-2 px-4;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 h-10 py-2 px-4;
  }
  
  .btn-ghost {
    @apply btn text-gray-700 hover:bg-gray-100 h-10 py-2 px-4;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 h-10 py-2 px-4;
  }
  
  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700 h-10 py-2 px-4;
  }
  
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-content {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .label {
    @apply text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70;
  }
  
  .sidebar-link {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors;
  }
  
  .sidebar-link-active {
    @apply sidebar-link bg-primary-100 text-primary-700;
  }
  
  .sidebar-link-inactive {
    @apply sidebar-link text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }
  
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }
  
  .badge-danger {
    @apply badge bg-red-100 text-red-800;
  }
  
  .badge-info {
    @apply badge bg-blue-100 text-blue-800;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  /* Agent Portal Specific Styles */
  .package-card {
    @apply card hover:shadow-md transition-shadow cursor-pointer;
  }
  
  .package-card:hover {
    @apply transform -translate-y-1;
  }
  
  .itinerary-item {
    @apply bg-white border border-gray-200 rounded-lg p-4 mb-4 shadow-sm;
  }
  
  .itinerary-day {
    @apply flex items-center justify-center w-8 h-8 bg-primary-600 text-white rounded-full text-sm font-medium;
  }
  
  .customer-avatar {
    @apply h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center text-white font-medium;
  }
  
  .notification-dot {
    @apply absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full;
  }
  
  .stats-card {
    @apply card p-6 text-center hover:shadow-md transition-shadow;
  }
  
  .stats-icon {
    @apply w-12 h-12 mx-auto mb-4 p-3 rounded-full;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
