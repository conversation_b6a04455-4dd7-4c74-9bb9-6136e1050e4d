import React, { useState, useEffect } from 'react';
import { 
  Package, 
  Calendar, 
  Users, 
  DollarSign, 
  TrendingUp,
  Activity,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Eye
} from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { dashboardService, AgentDashboardData } from '../services/dashboardService';
import { PageLoading } from '../components/LoadingSpinner';
import { formatCurrency, formatNumber, formatDate } from '../../../shared/utils';
import toast from 'react-hot-toast';

const COLORS = ['#10B981', '#F59E0B', '#EF4444', '#3B82F6'];

interface StatCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ElementType;
  color: string;
  onClick?: () => void;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, change, icon: Icon, color, onClick }) => (
  <div className={`stats-card ${onClick ? 'cursor-pointer hover:shadow-lg' : ''}`} onClick={onClick}>
    <div className={`stats-icon ${color}`}>
      <Icon className="w-6 h-6 text-white" />
    </div>
    <div className="text-2xl font-bold text-gray-900 mb-1">{value}</div>
    <div className="text-sm text-gray-600 mb-2">{title}</div>
    {change !== undefined && (
      <div className={`flex items-center text-sm ${
        change >= 0 ? 'text-green-600' : 'text-red-600'
      }`}>
        <TrendingUp className={`w-4 h-4 mr-1 ${
          change >= 0 ? 'text-green-500' : 'text-red-500 transform rotate-180'
        }`} />
        <span>{Math.abs(change)}% from last month</span>
      </div>
    )}
  </div>
);

export const Dashboard: React.FC = () => {
  const [data, setData] = useState<AgentDashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [upcomingTasks, setUpcomingTasks] = useState<any[]>([]);

  useEffect(() => {
    loadDashboardData();
    loadUpcomingTasks();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const dashboardData = await dashboardService.getAgentDashboard();
      setData(dashboardData);
      setError('');
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to load dashboard data';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUpcomingTasks = async () => {
    try {
      const tasks = await dashboardService.getUpcomingTasks();
      setUpcomingTasks(tasks);
    } catch (error) {
      console.error('Failed to load upcoming tasks:', error);
    }
  };

  const handleExport = async (type: 'bookings' | 'revenue' | 'customers') => {
    try {
      const blob = await dashboardService.exportDashboardData(type);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${type}-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success(`${type} data exported successfully`);
    } catch (error) {
      toast.error(`Failed to export ${type} data`);
    }
  };

  if (isLoading) {
    return <PageLoading message="Loading dashboard..." />;
  }

  if (error || !data) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <Activity className="mx-auto h-12 w-12" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load dashboard</h3>
        <p className="text-gray-500 mb-4">{error}</p>
        <button onClick={loadDashboardData} className="btn-primary">
          Try Again
        </button>
      </div>
    );
  }

  const stats = [
    {
      title: 'Total Packages',
      value: formatNumber(data.overview.totalPackages),
      icon: Package,
      color: 'bg-blue-500',
      onClick: () => window.location.href = '/packages',
    },
    {
      title: 'Active Bookings',
      value: formatNumber(data.overview.activeBookings),
      icon: Calendar,
      color: 'bg-green-500',
      onClick: () => window.location.href = '/bookings?status=confirmed',
    },
    {
      title: 'Total Customers',
      value: formatNumber(data.overview.customerCount),
      icon: Users,
      color: 'bg-purple-500',
      onClick: () => window.location.href = '/customers',
    },
    {
      title: 'Monthly Revenue',
      value: formatCurrency(data.overview.monthlyRevenue),
      icon: DollarSign,
      color: 'bg-yellow-500',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back! Here's what's happening with your travel business.
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => window.location.href = '/packages/new'}
            className="btn-primary"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Package
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      {/* Quick Actions & Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
          </div>
          <div className="card-content space-y-3">
            <button className="w-full btn-outline justify-start">
              <Plus className="w-4 h-4 mr-2" />
              Create New Package
            </button>
            <button className="w-full btn-outline justify-start">
              <Calendar className="w-4 h-4 mr-2" />
              View Bookings
            </button>
            <button className="w-full btn-outline justify-start">
              <Users className="w-4 h-4 mr-2" />
              Manage Customers
            </button>
            <button className="w-full btn-outline justify-start">
              <Activity className="w-4 h-4 mr-2" />
              View Analytics
            </button>
          </div>
        </div>

        {/* Performance Overview */}
        <div className="lg:col-span-2 card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Performance Overview</h3>
          </div>
          <div className="card-content">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{data.overview.averageRating}</div>
                <div className="text-sm text-gray-500 flex items-center justify-center">
                  <Star className="w-4 h-4 text-yellow-400 mr-1" />
                  Average Rating
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{formatCurrency(data.overview.totalRevenue)}</div>
                <div className="text-sm text-gray-500">Total Revenue</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Performance Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Monthly Performance</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={data.chartData.monthlyBookings}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="bookings" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Booking Status Distribution */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Booking Status</h3>
          </div>
          <div className="card-content">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={data.chartData.bookingsByStatus}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ status, count }) => `${status}: ${count}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {data.chartData.bookingsByStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Recent Activity and Top Packages */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Bookings */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Recent Bookings</h3>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {data.recentBookings.map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{booking.packageName}</p>
                    <p className="text-xs text-gray-500">{booking.customerName}</p>
                    <p className="text-xs text-gray-400">{formatDate(booking.travelDate)}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{formatCurrency(booking.amount)}</p>
                    <span className={`badge ${
                      booking.status === 'confirmed' ? 'badge-success' : 
                      booking.status === 'pending' ? 'badge-warning' : 'badge-danger'
                    }`}>
                      {booking.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Top Packages */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Top Performing Packages</h3>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {data.topPackages.map((package_, index) => (
                <div key={package_.id} className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center">
                      <span className="text-sm font-medium text-white">
                        #{index + 1}
                      </span>
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {package_.name}
                    </p>
                    <p className="text-sm text-gray-500 truncate">
                      {package_.destination}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {formatCurrency(package_.revenue)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {package_.bookings} bookings
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Tasks */}
      {upcomingTasks.length > 0 && (
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">Upcoming Tasks</h3>
          </div>
          <div className="card-content">
            <div className="space-y-3">
              {upcomingTasks.map((task) => (
                <div key={task.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      task.priority === 'high' ? 'bg-red-500' :
                      task.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                    }`} />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{task.title}</p>
                      <p className="text-xs text-gray-500">{task.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">{formatDate(task.dueDate)}</p>
                    <span className={`badge ${
                      task.priority === 'high' ? 'badge-danger' :
                      task.priority === 'medium' ? 'badge-warning' : 'badge-success'
                    }`}>
                      {task.priority}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
