import {
    Clock,
    Copy,
    Edit,
    Eye,
    Filter,
    MapPin,
    Plus,
    Search,
    ToggleLeft,
    ToggleRight,
    Trash2,
    Users,
    Package
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { TravelPackage } from '../../../shared/types';
import { PageLoading } from '../components/LoadingSpinner';
import { PackageFilters, packageService } from '../services/packageService';
import { formatCurrency } from '../utils';

export const Packages: React.FC = () => {
  const [packages, setPackages] = useState<TravelPackage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadPackages();
  }, [currentPage, categoryFilter]);

  const loadPackages = async () => {
    try {
      setIsLoading(true);
      const filters: PackageFilters = {
        page: currentPage,
        limit: 12,
        search: searchTerm || undefined,
        category: categoryFilter === 'all' ? undefined : categoryFilter,
      };
      
      const response = await packageService.getMyPackages(filters);
      setPackages(response.packages);
      setTotalPages(response.pagination.pages);
    } catch (error: any) {
      toast.error(error.message || 'Failed to load packages');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    loadPackages();
  };

  const handleToggleStatus = async (packageId: string, currentStatus: boolean) => {
    try {
      await packageService.togglePackageStatus(packageId, !currentStatus);
      toast.success(`Package ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
      loadPackages();
    } catch (error: any) {
      toast.error(error.message || 'Failed to update package status');
    }
  };

  const handleDuplicatePackage = async (packageId: string) => {
    try {
      await packageService.duplicatePackage(packageId);
      toast.success('Package duplicated successfully');
      loadPackages();
    } catch (error: any) {
      toast.error(error.message || 'Failed to duplicate package');
    }
  };

  const handleDeletePackage = async (packageId: string) => {
    if (!window.confirm('Are you sure you want to delete this package?')) {
      return;
    }

    try {
      await packageService.deletePackage(packageId);
      toast.success('Package deleted successfully');
      loadPackages();
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete package');
    }
  };

  if (isLoading && packages.length === 0) {
    return <PageLoading message="Loading packages..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Travel Packages</h1>
          <p className="mt-1 text-sm text-gray-500">
            Create and manage your travel packages
          </p>
        </div>
        <button className="btn-primary">
          <Plus className="w-4 h-4 mr-2" />
          Create Package
        </button>
      </div>

      {/* Filters */}
      <div className="card">
        <div className="card-content">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search packages..."
                  className="input pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                className="input"
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
              >
                <option value="all">All Categories</option>
                <option value="adventure">Adventure</option>
                <option value="relaxation">Relaxation</option>
                <option value="cultural">Cultural</option>
                <option value="business">Business</option>
                <option value="family">Family</option>
              </select>
              <button onClick={handleSearch} className="btn-primary">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Packages Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {packages.map((package_) => (
          <div key={package_.id} className="package-card">
            <div className="relative">
              <img
                src={package_.images[0] || '/placeholder-image.jpg'}
                alt={package_.name}
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <div className="absolute top-2 right-2">
                <span className={`badge ${package_.isActive ? 'badge-success' : 'badge-danger'}`}>
                  {package_.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div className="absolute top-2 left-2">
                <span className="badge badge-primary">
                  {package_.category}
                </span>
              </div>
            </div>
            
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{package_.name}</h3>
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">{package_.description}</p>
              
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-500">
                  <MapPin className="w-4 h-4 mr-2" />
                  {package_.destination}
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="w-4 h-4 mr-2" />
                  {package_.duration} days
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Users className="w-4 h-4 mr-2" />
                  Max {package_.maxGroupSize} people
                </div>
              </div>
              
              <div className="flex items-center justify-between mb-4">
                <div className="text-2xl font-bold text-primary-600">
                  {formatCurrency(package_.price)}
                </div>
                <div className="text-sm text-gray-500">
                  per person
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex space-x-2">
                  <button
                    onClick={() => window.location.href = `/packages/${package_.id}`}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
                    title="View Details"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => window.location.href = `/packages/${package_.id}/edit`}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
                    title="Edit Package"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDuplicatePackage(package_.id)}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
                    title="Duplicate Package"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleToggleStatus(package_.id, package_.isActive)}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
                    title={package_.isActive ? 'Deactivate' : 'Activate'}
                  >
                    {package_.isActive ? <ToggleRight className="w-4 h-4" /> : <ToggleLeft className="w-4 h-4" />}
                  </button>
                  <button
                    onClick={() => handleDeletePackage(package_.id)}
                    className="p-2 text-red-400 hover:text-red-600 hover:bg-red-50 rounded"
                    title="Delete Package"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {packages.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No packages found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first travel package.
          </p>
          <div className="mt-6">
            <button className="btn-primary">
              <Plus className="w-4 h-4 mr-2" />
              Create Package
            </button>
          </div>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="btn-outline disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="btn-outline disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
