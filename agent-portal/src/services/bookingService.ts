import { Booking } from '../../../shared/types';
import { apiService } from './api';

export interface BookingListResponse {
  bookings: Booking[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface BookingFilters {
  page?: number;
  limit?: number;
  status?: string;
  consumerId?: string;
  packageId?: string;
}

export interface BookingStats {
  totalBookings: number;
  confirmedBookings: number;
  pendingBookings: number;
  totalRevenue: number;
  paidRevenue: number;
  pendingRevenue: number;
}

class BookingService {
  async getBookings(filters: BookingFilters = {}): Promise<BookingListResponse> {
    const params = new URLSearchParams();
    
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.status) params.append('status', filters.status);
    if (filters.consumerId) params.append('consumerId', filters.consumerId);
    if (filters.packageId) params.append('packageId', filters.packageId);

    const response = await apiService.get<BookingListResponse>(`/bookings?${params.toString()}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch bookings');
    }
    
    return response.data;
  }

  async getBooking(id: string): Promise<Booking> {
    const response = await apiService.get<Booking>(`/bookings/${id}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch booking');
    }
    
    return response.data;
  }

  async updateBooking(id: string, bookingData: Partial<Booking>): Promise<Booking> {
    const response = await apiService.put<Booking>(`/bookings/${id}`, bookingData);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update booking');
    }
    
    return response.data;
  }

  async updateBookingStatus(id: string, status: string): Promise<Booking> {
    const response = await apiService.put<Booking>(`/bookings/${id}`, { status });
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update booking status');
    }
    
    return response.data;
  }

  async updatePaymentStatus(id: string, paymentStatus: string, paidAmount?: number): Promise<Booking> {
    const updateData: any = { paymentStatus };
    if (paidAmount !== undefined) {
      updateData.paidAmount = paidAmount;
    }

    const response = await apiService.put<Booking>(`/bookings/${id}`, updateData);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update payment status');
    }
    
    return response.data;
  }

  async cancelBooking(id: string, reason?: string): Promise<void> {
    const response = await apiService.delete(`/bookings/${id}`);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to cancel booking');
    }
  }

  async getBookingStats(): Promise<BookingStats> {
    const response = await apiService.get<BookingStats>('/bookings/stats/overview');
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch booking statistics');
    }
    
    return response.data;
  }

  async getUpcomingBookings(limit: number = 10): Promise<Booking[]> {
    const response = await apiService.get<BookingListResponse>(`/bookings?status=confirmed&limit=${limit}&sortBy=travelDate&sortOrder=asc`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch upcoming bookings');
    }
    
    return response.data.bookings;
  }

  async getPendingBookings(limit: number = 10): Promise<Booking[]> {
    const response = await apiService.get<BookingListResponse>(`/bookings?status=pending&limit=${limit}&sortBy=createdAt&sortOrder=desc`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch pending bookings');
    }
    
    return response.data.bookings;
  }

  async exportBookings(format: 'csv' | 'xlsx' = 'csv', filters?: BookingFilters): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      params.append('format', format);
      
      if (filters?.status) params.append('status', filters.status);
      if (filters?.packageId) params.append('packageId', filters.packageId);

      const response = await apiService.getApiInstance().get(`/bookings/export?${params.toString()}`, {
        responseType: 'blob',
      });
      
      return response.data;
    } catch (error) {
      throw new Error('Failed to export bookings');
    }
  }
}

export const bookingService = new BookingService();
