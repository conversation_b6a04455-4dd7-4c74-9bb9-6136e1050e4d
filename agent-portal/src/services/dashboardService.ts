import { apiService } from './api';

export interface AgentDashboardOverview {
  totalPackages: number;
  activePackages: number;
  totalBookings: number;
  activeBookings: number;
  pendingBookings: number;
  completedBookings: number;
  monthlyRevenue: number;
  totalRevenue: number;
  customerCount: number;
  averageRating: number;
}

export interface RecentBooking {
  id: string;
  packageName: string;
  customerName: string;
  travelDate: string;
  status: string;
  amount: number;
  travelers: number;
}

export interface TopPackage {
  id: string;
  name: string;
  destination: string;
  bookings: number;
  revenue: number;
  price: number;
}

export interface ChartData {
  monthlyBookings: Array<{
    month: string;
    bookings: number;
    revenue: number;
  }>;
  bookingsByStatus: Array<{
    status: string;
    count: number;
  }>;
}

export interface AgentDashboardData {
  overview: AgentDashboardOverview;
  recentBookings: RecentBooking[];
  topPackages: TopPackage[];
  chartData: ChartData;
}

class DashboardService {
  async getAgentDashboard(): Promise<AgentDashboardData> {
    const response = await apiService.get<AgentDashboardData>('/dashboard/agent');
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch dashboard data');
    }
    
    return response.data;
  }

  async getRevenueAnalytics(period: 'week' | 'month' | 'quarter' | 'year' = 'month'): Promise<any> {
    const response = await apiService.get(`/dashboard/agent/revenue?period=${period}`);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch revenue analytics');
    }
    
    return response.data;
  }

  async getCustomerAnalytics(): Promise<any> {
    const response = await apiService.get('/dashboard/agent/customers');
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch customer analytics');
    }
    
    return response.data;
  }

  async getPackagePerformance(): Promise<any> {
    const response = await apiService.get('/dashboard/agent/packages/performance');
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch package performance');
    }
    
    return response.data;
  }

  async exportDashboardData(type: 'bookings' | 'revenue' | 'customers', format: 'csv' | 'xlsx' = 'csv'): Promise<Blob> {
    try {
      const response = await apiService.getApiInstance().get(`/dashboard/agent/export/${type}`, {
        params: { format },
        responseType: 'blob',
      });
      
      return response.data;
    } catch (error) {
      throw new Error(`Failed to export ${type} data`);
    }
  }

  async getNotificationSummary(): Promise<any> {
    const response = await apiService.get('/notifications/stats/overview');
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to fetch notification summary');
    }
    
    return response.data;
  }

  async getUpcomingTasks(): Promise<any[]> {
    // Mock data for upcoming tasks
    return [
      {
        id: '1',
        title: 'Follow up with John Doe',
        description: 'Check payment status for Himalayan Trek',
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        priority: 'high',
        type: 'follow_up',
      },
      {
        id: '2',
        title: 'Prepare itinerary for Bali trip',
        description: 'Create detailed itinerary for Wilson family',
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        priority: 'medium',
        type: 'itinerary',
      },
      {
        id: '3',
        title: 'Update package pricing',
        description: 'Review and update pricing for summer packages',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        priority: 'low',
        type: 'admin',
      },
    ];
  }
}

export const dashboardService = new DashboardService();
