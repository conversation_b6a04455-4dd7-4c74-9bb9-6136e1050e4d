import { TravelPackage } from '../../../shared/types';
import { apiService } from './api';

export interface PackageListResponse {
  packages: TravelPackage[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface PackageFilters {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  destination?: string;
  minPrice?: number;
  maxPrice?: number;
}

class PackageService {
  async getPackages(filters: PackageFilters = {}): Promise<PackageListResponse> {
    const params = new URLSearchParams();
    
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.category) params.append('category', filters.category);
    if (filters.destination) params.append('destination', filters.destination);
    if (filters.minPrice) params.append('minPrice', filters.minPrice.toString());
    if (filters.maxPrice) params.append('maxPrice', filters.maxPrice.toString());

    const response = await apiService.get<PackageListResponse>(`/packages?${params.toString()}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch packages');
    }
    
    return response.data;
  }

  async getMyPackages(filters: PackageFilters = {}): Promise<PackageListResponse> {
    const params = new URLSearchParams();
    
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.category) params.append('category', filters.category);
    
    // Add agentId to get only current agent's packages
    const user = JSON.parse(localStorage.getItem('travelease_user_data') || '{}');
    if (user.id) {
      params.append('agentId', user.id);
    }

    const response = await apiService.get<PackageListResponse>(`/packages?${params.toString()}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch packages');
    }
    
    return response.data;
  }

  async getPackage(id: string): Promise<TravelPackage> {
    const response = await apiService.get<TravelPackage>(`/packages/${id}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch package');
    }
    
    return response.data;
  }

  async createPackage(packageData: Partial<TravelPackage>): Promise<TravelPackage> {
    const response = await apiService.post<TravelPackage>('/packages', packageData);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to create package');
    }
    
    return response.data;
  }

  async updatePackage(id: string, packageData: Partial<TravelPackage>): Promise<TravelPackage> {
    const response = await apiService.put<TravelPackage>(`/packages/${id}`, packageData);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update package');
    }
    
    return response.data;
  }

  async deletePackage(id: string): Promise<void> {
    const response = await apiService.delete(`/packages/${id}`);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to delete package');
    }
  }

  async uploadPackageImages(packageId: string, images: File[]): Promise<string[]> {
    const response = await apiService.uploadFiles<{ images: Array<{ url: string }> }>('/upload/package-images', images);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to upload images');
    }
    
    return response.data.images.map((img: any) => img.url);
  }

  async searchPackages(query: string): Promise<TravelPackage[]> {
    const response = await apiService.get<PackageListResponse>(`/packages/search/advanced?query=${encodeURIComponent(query)}&limit=50`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to search packages');
    }
    
    return response.data.packages;
  }

  async duplicatePackage(id: string): Promise<TravelPackage> {
    const originalPackage = await this.getPackage(id);
    
    const duplicatedData = {
      ...originalPackage,
      name: `${originalPackage.name} (Copy)`,
      id: undefined, // Remove ID so a new one is generated
      createdAt: undefined,
      updatedAt: undefined,
    };
    
    return this.createPackage(duplicatedData);
  }

  async togglePackageStatus(id: string, isActive: boolean): Promise<TravelPackage> {
    const response = await apiService.put<TravelPackage>(`/packages/${id}`, { isActive });
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to update package status');
    }
    
    return response.data;
  }
}

export const packageService = new PackageService();
