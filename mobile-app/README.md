# TravelEase Mobile App

A React Native mobile application for TravelEase travel booking platform, built with Expo and TypeScript.

## Features

- **Authentication**: Login, Register, Forgot Password with social login options
- **Trip Management**: View trips, trip details with itinerary, status tracking
- **Notifications**: Real-time notifications for trip updates, reminders
- **Profile Management**: User profile, settings, preferences
- **Modern UI**: Clean, intuitive interface based on Material Design
- **Offline Support**: Basic offline functionality with secure storage
- **Push Notifications**: Trip reminders and updates

## Tech Stack

- **React Native** with **Expo** (SDK 49)
- **TypeScript** for type safety
- **React Navigation** for navigation
- **React Native Paper** for UI components
- **Zustand** for state management
- **Axios** for API calls
- **Expo Secure Store** for secure data storage
- **React Hook Form** for form handling

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mobile-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Install fonts**
   Download Inter font family from Google Fonts and place in `assets/fonts/`:
   - Inter-Regular.ttf
   - Inter-Medium.ttf
   - Inter-SemiBold.ttf
   - Inter-Bold.ttf

4. **Configure environment**
   Update the API base URL in `src/constants/index.ts`:
   ```typescript
   export const API_BASE_URL = __DEV__ 
     ? 'http://localhost:3001/api'  // Your local backend URL
     : 'https://api.travelease.com/api';  // Production URL
   ```

## Development

1. **Start the development server**
   ```bash
   npm start
   # or
   yarn start
   ```

2. **Run on specific platform**
   ```bash
   # iOS
   npm run ios
   
   # Android
   npm run android
   
   # Web
   npm run web
   ```

## Project Structure

```
src/
├── components/          # Reusable UI components
├── constants/          # App constants, theme, colors
├── navigation/         # Navigation configuration
├── screens/           # Screen components
│   ├── auth/         # Authentication screens
│   ├── main/         # Main tab screens
│   ├── trips/        # Trip-related screens
│   ├── booking/      # Booking screens
│   └── chat/         # Chat screens
├── services/          # API services
├── store/            # State management (Zustand)
├── types/            # TypeScript type definitions
└── utils/            # Utility functions
```

## Key Features Implementation

### Authentication Flow
- Secure token storage using Expo Secure Store
- Automatic token refresh
- Social login integration ready
- Form validation with error handling

### Trip Management
- Trip listing with status filters
- Detailed trip view with itinerary
- Image gallery for trip photos
- Pickup information and timeline

### Navigation
- Stack and Tab navigation
- Deep linking support
- Header customization
- Gesture handling

### State Management
- Zustand for global state
- Persistent authentication state
- Optimistic updates
- Error handling

## API Integration

The app is designed to work with the TravelEase backend API. Key endpoints:

- `POST /auth/login` - User authentication
- `GET /trips` - Get user trips
- `GET /trips/:id` - Get trip details
- `GET /notifications` - Get notifications
- `PATCH /notifications/:id/read` - Mark notification as read

## Styling

The app uses a consistent design system:

- **Colors**: Primary cyan (#00BCD4), secondary orange (#FF6B35)
- **Typography**: Inter font family with consistent sizing
- **Spacing**: 8px grid system
- **Shadows**: Consistent elevation system
- **Components**: Material Design principles

## Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## Building for Production

### Android
```bash
# Build APK
npm run build:android

# Submit to Play Store
npm run submit:android
```

### iOS
```bash
# Build IPA
npm run build:ios

# Submit to App Store
npm run submit:ios
```

## Environment Variables

Create a `.env` file in the root directory:

```env
EXPO_PUBLIC_API_URL=https://api.travelease.com
EXPO_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
EXPO_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please contact the development team or create an issue in the repository.
