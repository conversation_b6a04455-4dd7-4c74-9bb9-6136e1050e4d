import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';

import { MainTabParamList, RootStackParamList } from '@/types';
import { colors } from '@/constants/theme';

// Tab Screens
import HomeScreen from '@/screens/main/HomeScreen';
import TripsScreen from '@/screens/main/TripsScreen';
import ProfileScreen from '@/screens/main/ProfileScreen';
import NotificationsScreen from '@/screens/main/NotificationsScreen';

// Stack Screens
import TripDetailsScreen from '@/screens/trips/TripDetailsScreen';
import PackageDetailsScreen from '@/screens/packages/PackageDetailsScreen';
import BookingFormScreen from '@/screens/booking/BookingFormScreen';
import PaymentScreen from '@/screens/booking/PaymentScreen';
import ChatScreen from '@/screens/chat/ChatScreen';

const Tab = createBottomTabNavigator<MainTabParamList>();
const Stack = createStackNavigator<RootStackParamList>();

const TabNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Trips') {
            iconName = focused ? 'airplane' : 'airplane-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else if (route.name === 'Notifications') {
            iconName = focused ? 'notifications' : 'notifications-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.text.secondary,
        tabBarStyle: {
          backgroundColor: colors.white,
          borderTopColor: colors.border,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontFamily: 'Inter-Medium',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{ tabBarLabel: 'Home' }}
      />
      <Tab.Screen 
        name="Trips" 
        component={TripsScreen}
        options={{ tabBarLabel: 'My Trips' }}
      />
      <Tab.Screen 
        name="Notifications" 
        component={NotificationsScreen}
        options={{ 
          tabBarLabel: 'Notifications',
          tabBarBadge: undefined, // You can add notification count here
        }}
      />
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ tabBarLabel: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

const MainNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: 'white' },
      }}
    >
      <Stack.Screen name="Main" component={TabNavigator} />
      <Stack.Screen 
        name="TripDetails" 
        component={TripDetailsScreen}
        options={{
          headerShown: true,
          headerTitle: 'Trip Details',
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.white,
          headerTitleStyle: {
            fontFamily: 'Inter-SemiBold',
          },
        }}
      />
      <Stack.Screen 
        name="PackageDetails" 
        component={PackageDetailsScreen}
        options={{
          headerShown: true,
          headerTitle: 'Package Details',
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.white,
          headerTitleStyle: {
            fontFamily: 'Inter-SemiBold',
          },
        }}
      />
      <Stack.Screen 
        name="BookingForm" 
        component={BookingFormScreen}
        options={{
          headerShown: true,
          headerTitle: 'Book Package',
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.white,
          headerTitleStyle: {
            fontFamily: 'Inter-SemiBold',
          },
        }}
      />
      <Stack.Screen 
        name="Payment" 
        component={PaymentScreen}
        options={{
          headerShown: true,
          headerTitle: 'Payment',
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.white,
          headerTitleStyle: {
            fontFamily: 'Inter-SemiBold',
          },
        }}
      />
      <Stack.Screen 
        name="Chat" 
        component={ChatScreen}
        options={{
          headerShown: true,
          headerTitle: 'Chat with Agent',
          headerStyle: {
            backgroundColor: colors.primary,
          },
          headerTintColor: colors.white,
          headerTitleStyle: {
            fontFamily: 'Inter-SemiBold',
          },
        }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
