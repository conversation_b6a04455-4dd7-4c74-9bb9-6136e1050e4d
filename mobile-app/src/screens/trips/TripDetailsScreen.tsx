import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { <PERSON><PERSON>, Chip } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { RootStackParamList, ItineraryItem } from '@/types';
import { colors, spacing, typography, shadows } from '@/constants/theme';

const { width } = Dimensions.get('window');

type TripDetailsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'TripDetails'>;
type TripDetailsScreenRouteProp = {
  key: string;
  name: 'TripDetails';
  params: { tripId: string };
};

const TripDetailsScreen: React.FC = () => {
  const navigation = useNavigation<TripDetailsScreenNavigationProp>();
  const route = useRoute<TripDetailsScreenRouteProp>();
  const { tripId } = route.params;

  const [selectedTab, setSelectedTab] = useState<'highlights' | 'itinerary'>('highlights');

  // Mock data - replace with actual API call
  const tripData = {
    id: tripId,
    title: 'Desert Safari',
    images: [
      'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=400',
      'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=400',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400',
    ],
    highlights: [
      'Experience high level of personalized services on this desert safari',
      'Feel the thrill of driving through an electrifying dune bash over colossal desert sands',
      'Take in the sunset views in the desert before heading to the desert camp area',
      'Enjoy a delicious BBQ dinner complete with both vegetarian and non-vegetarian dishes',
      'Watch traditional Arabic performances including camel rides, henna tattoo sessions and more',
    ],
    pickupInfo: {
      from: 'City Seasons (Hotel)',
      time: '14:30 PM',
      mode: 'Land Cruiser',
      travelTime: '01 Hour 15 Minutes',
      chauffeur: 'Karim Ul Haq',
    },
    itinerary: [
      {
        id: '1',
        day: 1,
        time: '14:30',
        title: 'Pick up',
        description: 'Exploration of the dunes in a 4x4 vehicle (25-30 minutes)',
        location: 'Dubai',
        type: 'transport' as const,
      },
      {
        id: '2',
        day: 1,
        time: '',
        title: 'Dubai',
        description: 'Take in the sunset views in the desert before heading to the desert camp area',
        location: 'Dubai',
        type: 'activity' as const,
      },
      {
        id: '3',
        day: 1,
        time: '',
        title: 'Dubai',
        description: 'Enjoy various activities including camel rides, henna tattoo sessions, and more. Sit down and have a traditional BBQ dinner with vegetarian and non-vegetarian cuisines with unlimited refreshments',
        location: 'Dubai',
        type: 'meal' as const,
      },
      {
        id: '4',
        day: 1,
        time: '20:30',
        title: 'To hotel/personal address',
        description: '',
        location: 'Dubai',
        type: 'transport' as const,
      },
    ],
  };

  const renderImageGallery = () => (
    <ScrollView
      horizontal
      pagingEnabled
      showsHorizontalScrollIndicator={false}
      style={styles.imageGallery}
    >
      {tripData.images.map((image, index) => (
        <Image key={index} source={{ uri: image }} style={styles.tripImage} />
      ))}
    </ScrollView>
  );

  const renderHighlights = () => (
    <View style={styles.highlightsContainer}>
      <Text style={styles.sectionTitle}>Highlights</Text>
      {tripData.highlights.map((highlight, index) => (
        <View key={index} style={styles.highlightItem}>
          <Text style={styles.highlightNumber}>{index + 1}.</Text>
          <Text style={styles.highlightText}>{highlight}</Text>
        </View>
      ))}
    </View>
  );

  const renderPickupInfo = () => (
    <View style={styles.pickupContainer}>
      <Text style={styles.pickupTitle}>Pickup From: {tripData.pickupInfo.from}</Text>
      <Text style={styles.pickupTime}>Pickup Time: {tripData.pickupInfo.time}</Text>
      <Text style={styles.pickupMode}>Mode of Pickup: {tripData.pickupInfo.mode}</Text>
      <Text style={styles.travelTime}>Travel Time: {tripData.pickupInfo.travelTime}</Text>
      <Text style={styles.chauffeur}>Chauffeur Details: {tripData.pickupInfo.chauffeur}</Text>
    </View>
  );

  const renderItinerary = () => (
    <View style={styles.itineraryContainer}>
      <Text style={styles.sectionTitle}>Itinerary</Text>
      {tripData.itinerary.map((item, index) => (
        <View key={item.id} style={styles.itineraryItem}>
          <View style={styles.itineraryIcon}>
            <View style={styles.timelineIcon}>
              <Ionicons
                name={
                  item.type === 'transport' ? 'car' :
                  item.type === 'activity' ? 'location' :
                  item.type === 'meal' ? 'restaurant' : 'time'
                }
                size={16}
                color={colors.primary}
              />
            </View>
            {index < tripData.itinerary.length - 1 && <View style={styles.timelineLine} />}
          </View>
          
          <View style={styles.itineraryContent}>
            <View style={styles.itineraryHeader}>
              {item.time && <Text style={styles.itineraryTime}>{item.time}</Text>}
              <Text style={styles.itineraryTitle}>{item.title}</Text>
            </View>
            <Text style={styles.itineraryLocation}>{item.location}</Text>
            {item.description && (
              <Text style={styles.itineraryDescription}>{item.description}</Text>
            )}
          </View>
        </View>
      ))}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Image Gallery */}
        {renderImageGallery()}

        {/* Content */}
        <View style={styles.content}>
          {/* Title */}
          <Text style={styles.title}>{tripData.title}</Text>

          {/* Tabs */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[styles.tab, selectedTab === 'highlights' && styles.activeTab]}
              onPress={() => setSelectedTab('highlights')}
            >
              <Text style={[styles.tabText, selectedTab === 'highlights' && styles.activeTabText]}>
                Highlights
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, selectedTab === 'itinerary' && styles.activeTab]}
              onPress={() => setSelectedTab('itinerary')}
            >
              <Text style={[styles.tabText, selectedTab === 'itinerary' && styles.activeTabText]}>
                Itinerary
              </Text>
            </TouchableOpacity>
          </View>

          {/* Tab Content */}
          {selectedTab === 'highlights' ? (
            <>
              {renderHighlights()}
              {renderPickupInfo()}
            </>
          ) : (
            renderItinerary()
          )}
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => navigation.navigate('Chat', { tripId })}
        >
          <Ionicons name="chatbubble-outline" size={24} color={colors.primary} />
        </TouchableOpacity>
        <Button
          mode="contained"
          style={styles.enjoyButton}
          contentStyle={styles.enjoyButtonContent}
          labelStyle={styles.enjoyButtonLabel}
          onPress={() => console.log('Enjoy the trip!')}
        >
          Enjoy the trip!
        </Button>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  imageGallery: {
    height: 250,
  },
  tripImage: {
    width,
    height: 250,
    resizeMode: 'cover',
  },
  content: {
    padding: spacing.lg,
  },
  title: {
    fontSize: typography.fontSize.xxl,
    fontFamily: typography.fontFamily.bold,
    color: colors.text.primary,
    marginBottom: spacing.lg,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: spacing.lg,
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.sm,
    alignItems: 'center',
    borderRadius: 6,
  },
  activeTab: {
    backgroundColor: colors.white,
    ...shadows.sm,
  },
  tabText: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.medium,
    color: colors.text.secondary,
  },
  activeTabText: {
    color: colors.text.primary,
  },
  sectionTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },
  highlightsContainer: {
    marginBottom: spacing.lg,
  },
  highlightItem: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },
  highlightNumber: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.primary,
    marginRight: spacing.sm,
    minWidth: 20,
  },
  highlightText: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.primary,
    flex: 1,
    lineHeight: typography.lineHeight.md,
  },
  pickupContainer: {
    backgroundColor: colors.surface,
    padding: spacing.md,
    borderRadius: 8,
    marginBottom: spacing.lg,
  },
  pickupTitle: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },
  pickupTime: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  pickupMode: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  travelTime: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  chauffeur: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
  },
  itineraryContainer: {
    marginBottom: spacing.lg,
  },
  itineraryItem: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  itineraryIcon: {
    alignItems: 'center',
    marginRight: spacing.md,
  },
  timelineIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: colors.border,
    marginTop: spacing.xs,
  },
  itineraryContent: {
    flex: 1,
  },
  itineraryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  itineraryTime: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.primary,
    marginRight: spacing.sm,
    minWidth: 50,
  },
  itineraryTitle: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.text.primary,
    flex: 1,
  },
  itineraryLocation: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  itineraryDescription: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.primary,
    lineHeight: typography.lineHeight.sm,
  },
  bottomActions: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    ...shadows.md,
  },
  actionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  enjoyButton: {
    flex: 1,
    backgroundColor: colors.primary,
  },
  enjoyButtonContent: {
    height: 48,
  },
  enjoyButtonLabel: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.white,
  },
});

export default TripDetailsScreen;
