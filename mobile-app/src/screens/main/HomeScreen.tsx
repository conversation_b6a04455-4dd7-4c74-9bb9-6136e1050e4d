import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Searchbar, Avatar, Badge } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { useAuth } from '@/store/authStore';
import { Trip, RootStackParamList } from '@/types';
import { colors, spacing, typography, shadows } from '@/constants/theme';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const { user } = useAuth();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [trips, setTrips] = useState<Trip[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Mock data - replace with actual API call
  const mockTrips: Trip[] = [
    {
      id: '1',
      title: 'Airport Pickup',
      description: 'Pickup from Terminal 2 to City Seasons Hotel',
      destination: 'City Seasons Hotel',
      startDate: '2024-02-10T10:00:00Z',
      endDate: '2024-02-10T11:00:00Z',
      status: 'completed',
      image: 'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=400',
      packageId: 'pkg1',
      bookingId: 'book1',
      totalAmount: 50,
      paidAmount: 50,
      remainingAmount: 0,
      participants: 2,
      pickupLocation: 'Terminal 2',
      pickupTime: '10:00 AM',
      agentId: 'agent1',
      agent: {
        id: 'agent1',
        name: 'City Seasons Hotel',
        phone: '+1234567890',
        email: '<EMAIL>',
      },
      itinerary: [],
      documents: [],
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-02-10T11:00:00Z',
    },
    {
      id: '2',
      title: 'Desert Safari',
      description: 'Experience the thrill of desert adventure',
      destination: 'Camp 54',
      startDate: '2024-02-11T14:30:00Z',
      endDate: '2024-02-11T20:00:00Z',
      status: 'today',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=400',
      packageId: 'pkg2',
      bookingId: 'book2',
      totalAmount: 150,
      paidAmount: 150,
      remainingAmount: 0,
      participants: 2,
      pickupLocation: 'City Seasons Hotel',
      pickupTime: '14:30 PM',
      agentId: 'agent1',
      agent: {
        id: 'agent1',
        name: 'City Seasons Hotel',
        phone: '+1234567890',
        email: '<EMAIL>',
      },
      itinerary: [],
      documents: [],
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-02-11T14:30:00Z',
    },
    {
      id: '3',
      title: 'Dubai Mall',
      description: 'Shopping and entertainment at Dubai Mall',
      destination: 'Dubai Mall',
      startDate: '2024-02-12T11:00:00Z',
      endDate: '2024-02-12T18:00:00Z',
      status: 'tomorrow',
      image: 'https://images.unsplash.com/photo-1555636222-cae831e670b3?w=400',
      packageId: 'pkg3',
      bookingId: 'book3',
      totalAmount: 75,
      paidAmount: 75,
      remainingAmount: 0,
      participants: 2,
      pickupLocation: 'City Seasons Hotel',
      pickupTime: '11:00 AM',
      agentId: 'agent1',
      agent: {
        id: 'agent1',
        name: 'City Seasons Hotel',
        phone: '+1234567890',
        email: '<EMAIL>',
      },
      itinerary: [],
      documents: [],
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-02-12T11:00:00Z',
    },
  ];

  useEffect(() => {
    setTrips(mockTrips);
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setTrips(mockTrips);
      setRefreshing(false);
    }, 1000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return colors.status.completed;
      case 'today':
        return colors.status.today;
      case 'tomorrow':
        return colors.status.tomorrow;
      case 'upcoming':
        return colors.status.upcoming;
      case 'cancelled':
        return colors.status.cancelled;
      default:
        return colors.text.secondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'today':
        return 'Today';
      case 'tomorrow':
        return 'Tomorrow';
      case 'upcoming':
        return 'Upcoming';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  };

  const renderTripCard = (trip: Trip) => (
    <TouchableOpacity
      key={trip.id}
      style={styles.tripCard}
      onPress={() => navigation.navigate('TripDetails', { tripId: trip.id })}
    >
      <Image source={{ uri: trip.image }} style={styles.tripImage} />
      
      <View style={styles.tripContent}>
        <View style={styles.tripHeader}>
          <Text style={styles.tripTitle}>{trip.title}</Text>
          <Badge
            style={[styles.statusBadge, { backgroundColor: getStatusColor(trip.status) }]}
          >
            {getStatusText(trip.status)}
          </Badge>
        </View>
        
        <Text style={styles.tripDescription}>
          From: {trip.agent.name}
        </Text>
        <Text style={styles.tripDescription}>
          To: {trip.destination}
        </Text>
        
        <View style={styles.tripFooter}>
          <View style={styles.timeContainer}>
            <Ionicons name="time-outline" size={16} color={colors.text.secondary} />
            <Text style={styles.timeText}>{formatTime(trip.startDate)}</Text>
          </View>
          
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="car-outline" size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="map-outline" size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="eye-outline" size={20} color={colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Ionicons name="ellipsis-horizontal" size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.greeting}>Hello {user?.firstName}!</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Profile')}>
            <Avatar.Text
              size={40}
              label={user?.firstName?.charAt(0) || 'U'}
              style={styles.avatar}
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search"
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={colors.text.secondary}
        />
      </View>

      {/* Trip List */}
      <ScrollView
        style={styles.tripList}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {trips.map(renderTripCard)}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  greeting: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.text.primary,
  },
  avatar: {
    backgroundColor: colors.primary,
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  searchBar: {
    backgroundColor: colors.surface,
    elevation: 0,
  },
  searchInput: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.regular,
  },
  tripList: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  tripCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: spacing.md,
    ...shadows.md,
  },
  tripImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  tripContent: {
    padding: spacing.md,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  tripTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.text.primary,
    flex: 1,
  },
  statusBadge: {
    marginLeft: spacing.sm,
  },
  tripDescription: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  tripFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.sm,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.medium,
    color: colors.text.secondary,
    marginLeft: spacing.xs,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default HomeScreen;
