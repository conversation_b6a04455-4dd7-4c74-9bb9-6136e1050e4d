import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Searchbar, Chip, Badge } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

import { Trip, RootStackParamList } from '@/types';
import { colors, spacing, typography, shadows } from '@/constants/theme';

type TripsScreenNavigationProp = StackNavigationProp<RootStackParamList>;

const TripsScreen: React.FC = () => {
  const navigation = useNavigation<TripsScreenNavigationProp>();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  const filters = [
    { key: 'all', label: 'All' },
    { key: 'upcoming', label: 'Upcoming' },
    { key: 'completed', label: 'Completed' },
    { key: 'cancelled', label: 'Cancelled' },
  ];

  // Mock data
  const trips: Trip[] = [
    {
      id: '1',
      title: 'Desert Safari Adventure',
      description: 'Experience the thrill of desert dunes',
      destination: 'Dubai Desert',
      startDate: '2024-02-15T14:30:00Z',
      endDate: '2024-02-15T20:00:00Z',
      status: 'upcoming',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=400',
      packageId: 'pkg1',
      bookingId: 'book1',
      totalAmount: 150,
      paidAmount: 150,
      remainingAmount: 0,
      participants: 2,
      agentId: 'agent1',
      agent: {
        id: 'agent1',
        name: 'Desert Adventures',
        phone: '+971501234567',
        email: '<EMAIL>',
      },
      itinerary: [],
      documents: [],
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-02-15T14:30:00Z',
    },
    {
      id: '2',
      title: 'Dubai City Tour',
      description: 'Explore the modern marvels of Dubai',
      destination: 'Dubai City',
      startDate: '2024-02-10T09:00:00Z',
      endDate: '2024-02-10T17:00:00Z',
      status: 'completed',
      image: 'https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=400',
      packageId: 'pkg2',
      bookingId: 'book2',
      totalAmount: 120,
      paidAmount: 120,
      remainingAmount: 0,
      participants: 2,
      agentId: 'agent2',
      agent: {
        id: 'agent2',
        name: 'City Tours Dubai',
        phone: '+971507654321',
        email: '<EMAIL>',
      },
      itinerary: [],
      documents: [],
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-02-10T17:00:00Z',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return colors.status.completed;
      case 'upcoming':
        return colors.status.upcoming;
      case 'cancelled':
        return colors.status.cancelled;
      default:
        return colors.text.secondary;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const filteredTrips = trips.filter(trip => {
    const matchesSearch = trip.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         trip.destination.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || trip.status === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  const renderTripCard = (trip: Trip) => (
    <TouchableOpacity
      key={trip.id}
      style={styles.tripCard}
      onPress={() => navigation.navigate('TripDetails', { tripId: trip.id })}
    >
      <Image source={{ uri: trip.image }} style={styles.tripImage} />
      
      <View style={styles.tripContent}>
        <View style={styles.tripHeader}>
          <Text style={styles.tripTitle}>{trip.title}</Text>
          <Badge
            style={[styles.statusBadge, { backgroundColor: getStatusColor(trip.status) }]}
          >
            {trip.status.charAt(0).toUpperCase() + trip.status.slice(1)}
          </Badge>
        </View>
        
        <Text style={styles.tripDestination}>{trip.destination}</Text>
        <Text style={styles.tripDate}>{formatDate(trip.startDate)}</Text>
        
        <View style={styles.tripFooter}>
          <View style={styles.participantsContainer}>
            <Ionicons name="people-outline" size={16} color={colors.text.secondary} />
            <Text style={styles.participantsText}>{trip.participants} travelers</Text>
          </View>
          
          <Text style={styles.tripAmount}>${trip.totalAmount}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Trips</Text>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search trips..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          inputStyle={styles.searchInput}
          iconColor={colors.text.secondary}
        />
      </View>

      {/* Filters */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filtersContainer}
        contentContainerStyle={styles.filtersContent}
      >
        {filters.map((filter) => (
          <Chip
            key={filter.key}
            selected={selectedFilter === filter.key}
            onPress={() => setSelectedFilter(filter.key)}
            style={[
              styles.filterChip,
              selectedFilter === filter.key && styles.selectedFilterChip,
            ]}
            textStyle={[
              styles.filterChipText,
              selectedFilter === filter.key && styles.selectedFilterChipText,
            ]}
          >
            {filter.label}
          </Chip>
        ))}
      </ScrollView>

      {/* Trip List */}
      <ScrollView
        style={styles.tripList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.tripListContent}
      >
        {filteredTrips.length > 0 ? (
          filteredTrips.map(renderTripCard)
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="airplane-outline" size={64} color={colors.text.secondary} />
            <Text style={styles.emptyStateTitle}>No trips found</Text>
            <Text style={styles.emptyStateSubtitle}>
              {searchQuery || selectedFilter !== 'all'
                ? 'Try adjusting your search or filters'
                : 'Start planning your next adventure!'}
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: typography.fontSize.xl,
    fontFamily: typography.fontFamily.bold,
    color: colors.text.primary,
  },
  searchContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  searchBar: {
    backgroundColor: colors.surface,
    elevation: 0,
  },
  searchInput: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.regular,
  },
  filtersContainer: {
    paddingVertical: spacing.sm,
  },
  filtersContent: {
    paddingHorizontal: spacing.lg,
    gap: spacing.sm,
  },
  filterChip: {
    backgroundColor: colors.surface,
    borderColor: colors.border,
  },
  selectedFilterChip: {
    backgroundColor: colors.primary,
  },
  filterChipText: {
    color: colors.text.primary,
    fontFamily: typography.fontFamily.medium,
  },
  selectedFilterChipText: {
    color: colors.white,
  },
  tripList: {
    flex: 1,
  },
  tripListContent: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.lg,
  },
  tripCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: spacing.md,
    ...shadows.md,
  },
  tripImage: {
    width: '100%',
    height: 150,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  tripContent: {
    padding: spacing.md,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  tripTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.text.primary,
    flex: 1,
  },
  statusBadge: {
    marginLeft: spacing.sm,
  },
  tripDestination: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
  tripDate: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    marginBottom: spacing.md,
  },
  tripFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  participantsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  participantsText: {
    fontSize: typography.fontSize.sm,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    marginLeft: spacing.xs,
  },
  tripAmount: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.bold,
    color: colors.primary,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyStateTitle: {
    fontSize: typography.fontSize.lg,
    fontFamily: typography.fontFamily.semiBold,
    color: colors.text.primary,
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  emptyStateSubtitle: {
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    textAlign: 'center',
  },
});

export default TripsScreen;
