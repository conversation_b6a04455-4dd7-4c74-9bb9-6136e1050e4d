export const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3001/api' 
  : 'https://api.travelease.com/api';

export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_DATA: 'user_data',
  REFRESH_TOKEN: 'refresh_token',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  NOTIFICATION_SETTINGS: 'notification_settings',
};

export const TRIP_STATUS = {
  UPCOMING: 'upcoming',
  TODAY: 'today',
  TOMORROW: 'tomorrow',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
} as const;

export const NOTIFICATION_TYPES = {
  TRIP_REMINDER: 'trip_reminder',
  TRIP_UPDATE: 'trip_update',
  PAYMENT_DUE: 'payment_due',
  BOOKING_CONFIRMED: 'booking_confirmed',
  WEATHER_ALERT: 'weather_alert',
} as const;

export const SCREEN_NAMES = {
  // Auth Stack
  LOGIN: 'Login',
  REGISTER: 'Register',
  FORGOT_PASSWORD: 'ForgotPassword',
  
  // Main Stack
  HOME: 'Home',
  TRIPS: 'Trips',
  PROFILE: 'Profile',
  NOTIFICATIONS: 'Notifications',
  
  // Trip Stack
  TRIP_DETAILS: 'TripDetails',
  TRIP_ITINERARY: 'TripItinerary',
  TRIP_DOCUMENTS: 'TripDocuments',
  TRIP_CHAT: 'TripChat',
  
  // Booking Stack
  SEARCH_PACKAGES: 'SearchPackages',
  PACKAGE_DETAILS: 'PackageDetails',
  BOOKING_FORM: 'BookingForm',
  BOOKING_CONFIRMATION: 'BookingConfirmation',
  PAYMENT: 'Payment',
  
  // Profile Stack
  EDIT_PROFILE: 'EditProfile',
  SETTINGS: 'Settings',
  HELP_SUPPORT: 'HelpSupport',
  ABOUT: 'About',
} as const;

export const ANIMATION_DURATION = {
  SHORT: 200,
  MEDIUM: 300,
  LONG: 500,
} as const;

export const DEVICE_DIMENSIONS = {
  SMALL_SCREEN_WIDTH: 320,
  MEDIUM_SCREEN_WIDTH: 375,
  LARGE_SCREEN_WIDTH: 414,
} as const;
