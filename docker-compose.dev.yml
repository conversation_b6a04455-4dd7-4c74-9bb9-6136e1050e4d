# Development override for docker-compose.yml
services:
  api-server:
    build:
      context: ./api-server
      dockerfile: Dockerfile.dev
    volumes:
      - ./api-server:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=3000
      - JWT_SECRET=dev-secret-key
    command: npm run dev

  admin-portal:
    build:
      context: ./admin-portal
      dockerfile: Dockerfile.dev
    volumes:
      - ./admin-portal:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8001
      - CHOKIDAR_USEPOLLING=true
    command: npm start

  agent-portal:
    build:
      context: ./agent-portal
      dockerfile: Dockerfile.dev
    volumes:
      - ./agent-portal:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8001
      - CHOKIDAR_USEPOLLING=true
    command: npm start

  consumer-app:
    volumes:
      - ./consumer-app:/app
      - /app/node_modules
    environment:
      - EXPO_PUBLIC_API_URL=http://localhost:8001
      - EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
