# TravelEase Environment Configuration

# API Server
API_PORT=8001
NODE_ENV=development
JWT_SECRET=your-super-secret-jwt-key-for-development

# Frontend Ports
ADMIN_PORTAL_PORT=8002
AGENT_PORTAL_PORT=8003
CONSUMER_APP_PORT=8004
EXPO_DEV_PORT=8005
EXPO_TOOLS_PORT=8006

# API URLs for frontends
REACT_APP_API_URL=http://localhost:8001
EXPO_PUBLIC_API_URL=http://localhost:8001

# CORS Origins
CORS_ORIGIN=http://localhost:8002,http://localhost:8003,http://localhost:8004

# Database (if you add one later)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=travelease
# DB_USER=postgres
# DB_PASSWORD=password
