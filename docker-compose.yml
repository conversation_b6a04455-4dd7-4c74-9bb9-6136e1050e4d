services:
  # API Server (Backend)
  api-server:
    build:
      context: ./api-server
      dockerfile: Dockerfile
    container_name: travelease-api
    ports:
      - "8001:3000"  # Custom port 8001 instead of default 3000
    environment:
      - NODE_ENV=development
      - PORT=3000
      - JWT_SECRET=your-super-secret-jwt-key-for-development
      - CORS_ORIGIN=http://localhost:8002,http://localhost:8003
    volumes:
      - ./api-server/uploads:/app/uploads
    networks:
      - travelease-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Super Admin Portal
  admin-portal:
    build:
      context: ./admin-portal
      dockerfile: Dockerfile
    container_name: travelease-admin
    ports:
      - "8002:80"  # Custom port 8002 for nginx
    environment:
      - REACT_APP_API_URL=http://localhost:8001
      - REACT_APP_ENV=development


    networks:
      - travelease-network
    depends_on:
      - api-server
    restart: unless-stopped

  # Travel Agent Portal
  agent-portal:
    build:
      context: ./agent-portal
      dockerfile: Dockerfile
    container_name: travelease-agent
    ports:
      - "8003:80"  # Custom port 8003 for nginx
    environment:
      - REACT_APP_API_URL=http://localhost:8001
      - REACT_APP_ENV=development


    networks:
      - travelease-network
    depends_on:
      - api-server
    restart: unless-stopped

  # Consumer Mobile App (Expo Web)
  consumer-app:
    build:
      context: ./consumer-app
      dockerfile: Dockerfile
    container_name: travelease-consumer
    ports:
      - "8004:19006"  # Custom port 8004 for Expo web
    environment:
      - EXPO_PUBLIC_API_URL=http://localhost:8001

    networks:
      - travelease-network
    depends_on:
      - api-server
    restart: unless-stopped
    stdin_open: true
    tty: true

networks:
  travelease-network:
    driver: bridge

volumes:
  node_modules_admin:
  node_modules_agent:
  node_modules_consumer:
  node_modules_api:
