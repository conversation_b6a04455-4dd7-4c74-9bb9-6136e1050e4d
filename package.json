{"name": "travelease", "version": "1.0.0", "description": "TravelEase - Complete travel management platform with admin portal, agent portal, consumer app, and API backend", "private": true, "workspaces": ["admin-portal", "agent-portal", "consumer-app", "api-server"], "scripts": {"install:all": "npm install && npm run install:admin && npm run install:agent && npm run install:consumer && npm run install:api", "install:admin": "cd admin-portal && npm install", "install:agent": "cd agent-portal && npm install", "install:consumer": "cd consumer-app && npm install", "install:api": "cd api-server && npm install", "dev:admin": "cd admin-portal && npm start", "dev:agent": "cd agent-portal && npm start", "dev:consumer": "cd consumer-app && npm start", "dev:api": "cd api-server && npm run dev", "build:all": "npm run build:admin && npm run build:agent && npm run build:consumer && npm run build:api", "build:admin": "cd admin-portal && npm run build", "build:agent": "cd agent-portal && npm run build", "build:consumer": "cd consumer-app && npm run build", "build:api": "cd api-server && npm run build", "docker:setup": "./scripts/docker-setup.sh", "docker:test": "./scripts/quick-test.sh"}, "keywords": ["travel", "booking", "management", "react", "react-native", "nodejs"], "author": "TravelEase Team", "license": "MIT"}