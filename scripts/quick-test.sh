#!/bin/bash

# Quick TravelEase Docker Test Script
echo "🚀 Quick TravelEase Docker Test"
echo "==============================="

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_test() {
    echo -e "${BLUE}Testing:${NC} $1"
}

print_success() {
    echo -e "${GREEN}✓ PASS:${NC} $1"
}

print_fail() {
    echo -e "${RED}✗ FAIL:${NC} $1"
}

print_info() {
    echo -e "${YELLOW}INFO:${NC} $1"
}

# Test API Server
print_test "API Server Health Check"
if curl -f http://localhost:8001/health &> /dev/null; then
    print_success "API Server is running on port 8001"
else
    print_fail "API Server is not responding on port 8001"
fi

# Test Admin Portal
print_test "Admin Portal Accessibility"
if curl -f http://localhost:8002 &> /dev/null; then
    print_success "Admin Portal is accessible on port 8002"
else
    print_fail "Admin Portal is not accessible on port 8002"
fi

# Test Agent Portal
print_test "Agent Portal Accessibility"
if curl -f http://localhost:8003 &> /dev/null; then
    print_success "Agent Portal is accessible on port 8003"
else
    print_fail "Agent Portal is not accessible on port 8003"
fi

# Test Consumer App
print_test "Consumer App Accessibility"
if curl -f http://localhost:8004 &> /dev/null; then
    print_success "Consumer App is accessible on port 8004"
else
    print_fail "Consumer App is not accessible on port 8004"
fi

# Test API Authentication
print_test "API Authentication Endpoint"
response=$(curl -s -X POST http://localhost:8001/api/auth/login \
  -H 'Content-Type: application/json' \
  -d '{"email":"<EMAIL>","password":"admin123"}' \
  -w "%{http_code}")

if [[ $response == *"200"* ]]; then
    print_success "API authentication is working"
else
    print_fail "API authentication failed"
fi

# Test Docker containers
print_test "Docker Container Status"
containers=$(docker-compose ps --services --filter "status=running" | wc -l)
if [ $containers -ge 4 ]; then
    print_success "All Docker containers are running"
else
    print_fail "Some Docker containers are not running"
    print_info "Run: docker-compose ps"
fi

echo ""
print_info "🌐 Application URLs:"
echo "   • API Server:     http://localhost:8001"
echo "   • Admin Portal:   http://localhost:8002"
echo "   • Agent Portal:   http://localhost:8003"
echo "   • Consumer App:   http://localhost:8004"

echo ""
print_info "🔐 Test Credentials:"
echo "   • Admin:    <EMAIL> / admin123"
echo "   • Agent:    <EMAIL> / agent123"
echo "   • Customer: <EMAIL> / customer123"

echo ""
print_info "📋 Next Steps:"
echo "   1. Open the URLs above in your browser"
echo "   2. Login with the provided credentials"
echo "   3. Run full testing guide: ./scripts/test-guide.sh"
echo "   4. Check detailed setup: ./scripts/verify-setup.sh"

echo ""
echo "🎉 Quick test completed!"
