#!/bin/bash

# TravelEase Build Testing Script
echo "🔨 Testing TravelEase Builds Before Docker"
echo "==========================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ SUCCESS:${NC} $1"
}

print_fail() {
    echo -e "${RED}✗ FAILED:${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0

test_build() {
    local app_name="$1"
    local app_dir="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_header "Testing $app_name Build"
    
    if [ ! -d "$app_dir" ]; then
        print_fail "$app_name directory not found"
        return 1
    fi
    
    cd "$app_dir"
    
    print_info "Installing dependencies for $app_name..."
    if npm install --legacy-peer-deps &> ../build-test.log; then
        print_success "$app_name dependencies installed"
    else
        print_fail "$app_name dependency installation failed"
        echo "Check ../build-test.log for details"
        cd ..
        return 1
    fi
    
    print_info "Building $app_name..."
    if npm run build &> ../build-test.log; then
        print_success "$app_name build completed"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        cd ..
        return 0
    else
        print_fail "$app_name build failed"
        echo "Check ../build-test.log for details"
        echo "Last 10 lines of error:"
        tail -10 ../build-test.log
        cd ..
        return 1
    fi
}

# Check if Node.js and npm are available
if ! command -v node &> /dev/null; then
    print_fail "Node.js is not installed"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_fail "npm is not installed"
    exit 1
fi

print_success "Node.js and npm are available"

# Test each application build
print_header "Starting Build Tests"

test_build "API Server" "api-server"
test_build "Admin Portal" "admin-portal"
test_build "Agent Portal" "agent-portal"
test_build "Consumer App" "consumer-app"

# Summary
print_header "Build Test Summary"

FAILED_TESTS=$((TOTAL_TESTS - PASSED_TESTS))

echo -e "\n${BLUE}Results:${NC}"
echo -e "Total Applications: $TOTAL_TESTS"
echo -e "${GREEN}Successful Builds: $PASSED_TESTS${NC}"
echo -e "${RED}Failed Builds: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All builds passed! Docker setup should work.${NC}"
    echo -e "${GREEN}✅ Ready to run: ./scripts/docker-setup.sh${NC}"
else
    echo -e "\n${YELLOW}⚠️  Some builds failed. Fix the issues before Docker setup.${NC}"
    echo -e "${YELLOW}💡 Check the error logs above and fix the dependency issues.${NC}"
fi

# Clean up
rm -f build-test.log

exit $FAILED_TESTS
