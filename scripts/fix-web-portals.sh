#!/bin/bash

# Fix Web Portals Script
echo "🔧 Fixing Web Portals..."

# Create a simple index.html for admin portal
/usr/local/bin/docker exec travelease-admin sh -c 'cat > /usr/share/nginx/html/index.html << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TravelEase Admin Portal</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #4F46E5; text-align: center; }
        .status { background: #10B981; color: white; padding: 10px; border-radius: 4px; text-align: center; margin: 20px 0; }
        .info { background: #EBF8FF; border: 1px solid #3B82F6; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .link { background: #4F46E5; color: white; padding: 15px; text-decoration: none; border-radius: 4px; text-align: center; }
        .link:hover { background: #4338CA; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 TravelEase Admin Portal</h1>
        <div class="status">✅ Service is running successfully!</div>
        
        <div class="info">
            <h3>🔐 Default Admin Credentials:</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> admin123</p>
        </div>
        
        <div class="info">
            <h3>🌐 Other Services:</h3>
            <div class="links">
                <a href="http://localhost:8001" class="link">API Server</a>
                <a href="http://localhost:8003" class="link">Agent Portal</a>
                <a href="http://localhost:8004" class="link">Consumer App</a>
            </div>
        </div>
        
        <div class="info">
            <h3>📋 Admin Features:</h3>
            <ul>
                <li>User Management</li>
                <li>Travel Package Management</li>
                <li>Booking Management</li>
                <li>Analytics Dashboard</li>
                <li>System Configuration</li>
            </ul>
        </div>
        
        <div class="info">
            <p><strong>Note:</strong> This is a temporary landing page. The full React application will be available once the build issues are resolved.</p>
        </div>
    </div>
</body>
</html>
EOF'

# Create a simple index.html for agent portal
/usr/local/bin/docker exec travelease-agent sh -c 'cat > /usr/share/nginx/html/index.html << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TravelEase Agent Portal</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #10B981; text-align: center; }
        .status { background: #10B981; color: white; padding: 10px; border-radius: 4px; text-align: center; margin: 20px 0; }
        .info { background: #EBF8FF; border: 1px solid #3B82F6; padding: 15px; border-radius: 4px; margin: 20px 0; }
        .links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .link { background: #10B981; color: white; padding: 15px; text-decoration: none; border-radius: 4px; text-align: center; }
        .link:hover { background: #059669; }
    </style>
</head>
<body>
    <div class="container">
        <h1>✈️ TravelEase Agent Portal</h1>
        <div class="status">✅ Service is running successfully!</div>
        
        <div class="info">
            <h3>🔐 Default Agent Credentials:</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> agent123</p>
        </div>
        
        <div class="info">
            <h3>🌐 Other Services:</h3>
            <div class="links">
                <a href="http://localhost:8001" class="link">API Server</a>
                <a href="http://localhost:8002" class="link">Admin Portal</a>
                <a href="http://localhost:8004" class="link">Consumer App</a>
            </div>
        </div>
        
        <div class="info">
            <h3>📋 Agent Features:</h3>
            <ul>
                <li>Package Creation & Management</li>
                <li>Customer Booking Management</li>
                <li>Commission Tracking</li>
                <li>Performance Dashboard</li>
                <li>Customer Communication</li>
            </ul>
        </div>
        
        <div class="info">
            <p><strong>Note:</strong> This is a temporary landing page. The full React application will be available once the build issues are resolved.</p>
        </div>
    </div>
</body>
</html>
EOF'

echo "✅ Web portals fixed with temporary landing pages"
echo "🌐 Admin Portal: http://localhost:8002"
echo "🌐 Agent Portal: http://localhost:8003"
