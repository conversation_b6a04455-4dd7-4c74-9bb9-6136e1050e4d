#!/bin/bash

# TravelEase Service Testing Script
echo "🧪 Testing TravelEase Services"
echo "=============================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✓ PASS:${NC} $1"
}

print_fail() {
    echo -e "${RED}✗ FAIL:${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Wait for services to start
print_info "Waiting for services to start..."
sleep 10

# Test API Server
print_test "Testing API Server (http://localhost:8001)"
if curl -f -s http://localhost:8001/health > /dev/null; then
    print_success "API Server is responding"
else
    print_fail "API Server is not responding"
fi

# Test Admin Portal
print_test "Testing Admin Portal (http://localhost:8002)"
if curl -f -s http://localhost:8002 > /dev/null; then
    print_success "Admin Portal is accessible"
else
    print_fail "Admin Portal is not accessible"
fi

# Test Agent Portal
print_test "Testing Agent Portal (http://localhost:8003)"
if curl -f -s http://localhost:8003 > /dev/null; then
    print_success "Agent Portal is accessible"
else
    print_fail "Agent Portal is not accessible"
fi

# Test Consumer App
print_test "Testing Consumer App (http://localhost:8004)"
if curl -f -s http://localhost:8004 > /dev/null; then
    print_success "Consumer App is accessible"
else
    print_fail "Consumer App is not accessible"
fi

# Test API Authentication
print_test "Testing API Authentication"
AUTH_RESPONSE=$(curl -s -X POST http://localhost:8001/api/auth/login \
    -H 'Content-Type: application/json' \
    -d '{"email":"<EMAIL>","password":"admin123"}')

if echo "$AUTH_RESPONSE" | grep -q "token"; then
    print_success "API authentication is working"
else
    print_fail "API authentication failed"
fi

echo ""
print_info "🌐 Application URLs:"
echo "   • API Server:     http://localhost:8001"
echo "   • Admin Portal:   http://localhost:8002"
echo "   • Agent Portal:   http://localhost:8003"
echo "   • Consumer App:   http://localhost:8004"

echo ""
print_info "🔐 Test Credentials:"
echo "   • Admin:    <EMAIL> / admin123"
echo "   • Agent:    <EMAIL> / agent123"
echo "   • Customer: <EMAIL> / customer123"

echo ""
print_info "📋 Next Steps:"
echo "   1. Open the URLs above in your browser"
echo "   2. Login with the provided credentials"
echo "   3. Test the complete user workflows"

echo ""
echo "🎉 Service testing completed!"
