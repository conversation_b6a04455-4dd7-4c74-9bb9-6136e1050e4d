#!/bin/bash

# TravelEase Setup Verification Script
echo "🔍 TravelEase Setup Verification"
echo "================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_check() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓${NC} $2"
    else
        echo -e "${RED}✗${NC} $2"
    fi
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check Docker installation
print_info "Checking Docker installation..."
docker --version &> /dev/null
print_check $? "Docker is installed"

docker-compose --version &> /dev/null
print_check $? "Docker Compose is installed"

# Check if Docker daemon is running
docker ps &> /dev/null
print_check $? "Docker daemon is running"

# Check project structure
print_info "Checking project structure..."
[ -d "api-server" ]
print_check $? "api-server directory exists"

[ -d "admin-portal" ]
print_check $? "admin-portal directory exists"

[ -d "agent-portal" ]
print_check $? "agent-portal directory exists"

[ -d "consumer-app" ]
print_check $? "consumer-app directory exists"

[ -d "shared" ]
print_check $? "shared directory exists"

# Check Docker files
print_info "Checking Docker configuration files..."
[ -f "docker-compose.yml" ]
print_check $? "docker-compose.yml exists"

[ -f "docker-compose.dev.yml" ]
print_check $? "docker-compose.dev.yml exists"

[ -f ".env.example" ]
print_check $? ".env.example exists"

# Check Dockerfiles
[ -f "api-server/Dockerfile" ]
print_check $? "API Server Dockerfile exists"

[ -f "admin-portal/Dockerfile" ]
print_check $? "Admin Portal Dockerfile exists"

[ -f "agent-portal/Dockerfile" ]
print_check $? "Agent Portal Dockerfile exists"

[ -f "consumer-app/Dockerfile" ]
print_check $? "Consumer App Dockerfile exists"

# Check scripts
print_info "Checking setup scripts..."
[ -f "scripts/docker-setup.sh" ]
print_check $? "docker-setup.sh exists"

[ -x "scripts/docker-setup.sh" ]
print_check $? "docker-setup.sh is executable"

[ -f "scripts/docker-dev.sh" ]
print_check $? "docker-dev.sh exists"

[ -x "scripts/docker-dev.sh" ]
print_check $? "docker-dev.sh is executable"

[ -f "scripts/test-guide.sh" ]
print_check $? "test-guide.sh exists"

[ -x "scripts/test-guide.sh" ]
print_check $? "test-guide.sh is executable"

# Check package.json files
print_info "Checking package.json files..."
[ -f "api-server/package.json" ]
print_check $? "API Server package.json exists"

[ -f "admin-portal/package.json" ]
print_check $? "Admin Portal package.json exists"

[ -f "agent-portal/package.json" ]
print_check $? "Agent Portal package.json exists"

[ -f "consumer-app/package.json" ]
print_check $? "Consumer App package.json exists"

# Check port availability
print_info "Checking port availability..."
ports=(************** **************)
for port in "${ports[@]}"; do
    if lsof -i :$port &> /dev/null; then
        print_warning "Port $port is already in use"
    else
        echo -e "${GREEN}✓${NC} Port $port is available"
    fi
done

# Check .env file
if [ -f ".env" ]; then
    echo -e "${GREEN}✓${NC} .env file exists"
else
    print_warning ".env file not found. Run: cp .env.example .env"
fi

echo ""
echo "🚀 Setup verification complete!"
echo ""
echo "Next steps:"
echo "1. If any checks failed, fix the issues"
echo "2. Run: ./scripts/docker-setup.sh"
echo "3. Run: ./scripts/test-guide.sh"
echo ""
echo "For development with hot reload:"
echo "   ./scripts/docker-dev.sh"
