#!/bin/bash

# TravelEase Development Docker Setup Script
echo "🔧 Setting up TravelEase Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    print_status "Creating .env file from .env.example..."
    cp .env.example .env
    print_success ".env file created"
fi

# Start development environment
print_status "Starting development environment with hot reload..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

print_success "Development environment started!"
echo ""
echo "📋 Development URLs:"
echo "   • API Server:     http://localhost:8001 (with hot reload)"
echo "   • Admin Portal:   http://localhost:8002 (with hot reload)"
echo "   • Agent Portal:   http://localhost:8003 (with hot reload)"
echo "   • Consumer App:   http://localhost:8004 (with hot reload)"
echo ""
echo "🔧 Development features:"
echo "   • Hot reload enabled for all services"
echo "   • Source code mounted as volumes"
echo "   • Development dependencies included"
echo "   • Detailed logging enabled"
