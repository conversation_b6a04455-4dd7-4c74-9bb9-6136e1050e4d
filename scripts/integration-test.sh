#!/bin/bash

# TravelEase Integration Test Script
echo "🔗 TravelEase Integration Testing"
echo "=================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✓ PASS:${NC} $1"
}

print_fail() {
    echo -e "${RED}✗ FAIL:${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_test "$test_name"
    
    if eval "$test_command" &> /dev/null; then
        print_success "$test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_fail "$test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Check if services are running
print_header "Service Availability Check"

# Check if API server is running
if curl -f http://localhost:8001/health &> /dev/null; then
    print_success "API Server is running on port 8001"
    API_RUNNING=true
else
    print_info "API Server is not running. Testing will be limited."
    API_RUNNING=false
fi

# Check frontend services
run_test "Admin Portal Accessibility" "curl -f http://localhost:8002"
run_test "Agent Portal Accessibility" "curl -f http://localhost:8003"
run_test "Consumer App Accessibility" "curl -f http://localhost:8004"

if [ "$API_RUNNING" = true ]; then
    print_header "API Integration Tests"
    
    # Test API endpoints
    run_test "API Health Check" "curl -f http://localhost:8001/health"
    run_test "API Auth Endpoint" "curl -f http://localhost:8001/api/auth/login -X POST -H 'Content-Type: application/json' -d '{\"email\":\"test\",\"password\":\"test\"}'"
    run_test "API Agents Endpoint" "curl -f http://localhost:8001/api/agents"
    run_test "API Packages Endpoint" "curl -f http://localhost:8001/api/packages"
    run_test "API Customers Endpoint" "curl -f http://localhost:8001/api/customers"
    run_test "API Bookings Endpoint" "curl -f http://localhost:8001/api/bookings"
    
    print_header "Authentication Flow Test"
    
    # Test authentication with valid credentials
    AUTH_RESPONSE=$(curl -s -X POST http://localhost:8001/api/auth/login \
        -H 'Content-Type: application/json' \
        -d '{"email":"<EMAIL>","password":"admin123"}')
    
    if echo "$AUTH_RESPONSE" | grep -q "token"; then
        print_success "Admin authentication successful"
        ADMIN_TOKEN=$(echo "$AUTH_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    else
        print_fail "Admin authentication failed"
    fi
    
    # Test agent authentication
    AGENT_AUTH_RESPONSE=$(curl -s -X POST http://localhost:8001/api/auth/login \
        -H 'Content-Type: application/json' \
        -d '{"email":"<EMAIL>","password":"agent123"}')
    
    if echo "$AGENT_AUTH_RESPONSE" | grep -q "token"; then
        print_success "Agent authentication successful"
        AGENT_TOKEN=$(echo "$AGENT_AUTH_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    else
        print_fail "Agent authentication failed"
    fi
    
    # Test customer authentication
    CUSTOMER_AUTH_RESPONSE=$(curl -s -X POST http://localhost:8001/api/auth/login \
        -H 'Content-Type: application/json' \
        -d '{"email":"<EMAIL>","password":"customer123"}')
    
    if echo "$CUSTOMER_AUTH_RESPONSE" | grep -q "token"; then
        print_success "Customer authentication successful"
        CUSTOMER_TOKEN=$(echo "$CUSTOMER_AUTH_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    else
        print_fail "Customer authentication failed"
    fi
    
    print_header "CRUD Operations Test"
    
    if [ ! -z "$ADMIN_TOKEN" ]; then
        # Test agent creation (Admin function)
        CREATE_AGENT_RESPONSE=$(curl -s -X POST http://localhost:8001/api/agents \
            -H 'Content-Type: application/json' \
            -H "Authorization: Bearer $ADMIN_TOKEN" \
            -d '{"name":"Test Agent","email":"<EMAIL>","phone":"+1234567890","company":"Test Co"}')
        
        if echo "$CREATE_AGENT_RESPONSE" | grep -q "id"; then
            print_success "Agent creation successful"
            AGENT_ID=$(echo "$CREATE_AGENT_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        else
            print_fail "Agent creation failed"
        fi
    fi
    
    if [ ! -z "$AGENT_TOKEN" ]; then
        # Test package creation (Agent function)
        CREATE_PACKAGE_RESPONSE=$(curl -s -X POST http://localhost:8001/api/packages \
            -H 'Content-Type: application/json' \
            -H "Authorization: Bearer $AGENT_TOKEN" \
            -d '{"title":"Test Package","description":"Test Description","destination":"Test City","duration":3,"price":299}')
        
        if echo "$CREATE_PACKAGE_RESPONSE" | grep -q "id"; then
            print_success "Package creation successful"
            PACKAGE_ID=$(echo "$CREATE_PACKAGE_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        else
            print_fail "Package creation failed"
        fi
        
        # Test customer creation (Agent function)
        CREATE_CUSTOMER_RESPONSE=$(curl -s -X POST http://localhost:8001/api/customers \
            -H 'Content-Type: application/json' \
            -H "Authorization: Bearer $AGENT_TOKEN" \
            -d '{"firstName":"Test","lastName":"Customer","email":"<EMAIL>","phone":"+1987654321"}')
        
        if echo "$CREATE_CUSTOMER_RESPONSE" | grep -q "id"; then
            print_success "Customer creation successful"
            CUSTOMER_ID=$(echo "$CREATE_CUSTOMER_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        else
            print_fail "Customer creation failed"
        fi
    fi
    
    print_header "Data Flow Integration Test"
    
    if [ ! -z "$PACKAGE_ID" ] && [ ! -z "$CUSTOMER_ID" ] && [ ! -z "$AGENT_TOKEN" ]; then
        # Test booking creation
        CREATE_BOOKING_RESPONSE=$(curl -s -X POST http://localhost:8001/api/bookings \
            -H 'Content-Type: application/json' \
            -H "Authorization: Bearer $AGENT_TOKEN" \
            -d "{\"packageId\":\"$PACKAGE_ID\",\"customerId\":\"$CUSTOMER_ID\",\"startDate\":\"2024-03-15\",\"participants\":2}")
        
        if echo "$CREATE_BOOKING_RESPONSE" | grep -q "id"; then
            print_success "Booking creation successful"
            BOOKING_ID=$(echo "$CREATE_BOOKING_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
        else
            print_fail "Booking creation failed"
        fi
    fi
    
    if [ ! -z "$CUSTOMER_TOKEN" ]; then
        # Test customer can view their bookings
        CUSTOMER_BOOKINGS=$(curl -s -H "Authorization: Bearer $CUSTOMER_TOKEN" \
            http://localhost:8001/api/bookings/my-bookings)
        
        if echo "$CUSTOMER_BOOKINGS" | grep -q "bookings"; then
            print_success "Customer can view their bookings"
        else
            print_fail "Customer cannot view their bookings"
        fi
    fi
    
else
    print_info "Skipping API integration tests - API server not running"
    print_info "To run full integration tests:"
    print_info "1. Start API server: cd api-server && npm run dev"
    print_info "2. Re-run this script"
fi

print_header "Configuration Validation"

# Check environment files
run_test "Admin Portal .env exists" "[ -f admin-portal/.env ]"
run_test "Agent Portal .env exists" "[ -f agent-portal/.env ]"
run_test "Consumer App .env exists" "[ -f consumer-app/.env ]"
run_test "API Server .env exists" "[ -f api-server/.env ]"

# Check API URLs in config files
if grep -q "8001" admin-portal/.env; then
    print_success "Admin Portal configured for correct API port"
else
    print_fail "Admin Portal API URL configuration issue"
fi

if grep -q "8001" agent-portal/.env; then
    print_success "Agent Portal configured for correct API port"
else
    print_fail "Agent Portal API URL configuration issue"
fi

if grep -q "8001" consumer-app/.env; then
    print_success "Consumer App configured for correct API port"
else
    print_fail "Consumer App API URL configuration issue"
fi

print_header "Package Dependencies Check"

# Check if all package.json files exist and have required dependencies
run_test "API Server package.json valid" "[ -f api-server/package.json ] && grep -q 'express' api-server/package.json"
run_test "Admin Portal package.json valid" "[ -f admin-portal/package.json ] && grep -q 'react' admin-portal/package.json"
run_test "Agent Portal package.json valid" "[ -f agent-portal/package.json ] && grep -q 'react' agent-portal/package.json"
run_test "Consumer App package.json valid" "[ -f consumer-app/package.json ] && grep -q 'expo' consumer-app/package.json"

print_header "Test Summary"

echo -e "\n${BLUE}Integration Test Results:${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All integration tests passed!${NC}"
    echo -e "${GREEN}✅ TravelEase is properly integrated and ready for use.${NC}"
else
    echo -e "\n${YELLOW}⚠️  Some tests failed. Please review the issues above.${NC}"
    echo -e "${YELLOW}💡 Common solutions:${NC}"
    echo -e "   • Ensure all services are running"
    echo -e "   • Check environment configurations"
    echo -e "   • Verify API endpoints are accessible"
    echo -e "   • Check CORS settings"
fi

echo -e "\n${BLUE}Next Steps:${NC}"
echo -e "1. Start all services: ./scripts/docker-setup.sh"
echo -e "2. Run manual testing: ./scripts/test-guide.sh"
echo -e "3. Access applications:"
echo -e "   • Admin Portal:   http://localhost:8002"
echo -e "   • Agent Portal:   http://localhost:8003"
echo -e "   • Consumer App:   http://localhost:8004"

exit $FAILED_TESTS
