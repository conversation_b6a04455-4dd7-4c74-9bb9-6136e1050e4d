#!/bin/bash

# TravelEase Docker Setup Script
echo "🚀 Setting up TravelEase Docker Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_status "Docker and Docker Compose are installed ✓"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    if [ -f .env.example ]; then
        print_status "Creating .env file from .env.example..."
        cp .env.example .env
        print_success ".env file created"
    else
        print_warning ".env.example not found, creating basic .env file..."
        cat > .env << EOF
# TravelEase Environment Configuration
API_PORT=8001
ADMIN_PORTAL_PORT=8002
AGENT_PORTAL_PORT=8003
CONSUMER_APP_PORT=8004
NODE_ENV=development
JWT_SECRET=dev-secret-key-for-travelease-2024
REACT_APP_API_URL=http://localhost:8001
EXPO_PUBLIC_API_URL=http://localhost:8001
CORS_ORIGIN=http://localhost:8002,http://localhost:8003,http://localhost:8004
EOF
        print_success "Basic .env file created"
    fi
else
    print_warning ".env file already exists"
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p api-server/uploads/avatars
mkdir -p api-server/uploads/packages
mkdir -p api-server/logs
print_success "Directories created"

# Check if package-lock.json files exist, if not, prepare them
print_status "Checking Docker build requirements..."
if [ ! -f "api-server/package-lock.json" ] || [ ! -f "admin-portal/package-lock.json" ] || [ ! -f "agent-portal/package-lock.json" ] || [ ! -f "consumer-app/package-lock.json" ]; then
    print_warning "package-lock.json files missing. Running Docker preparation..."
    if command -v node &> /dev/null && command -v npm &> /dev/null; then
        ./scripts/prepare-docker.sh
        if [ $? -ne 0 ]; then
            print_error "Docker preparation failed"
            exit 1
        fi
    else
        print_error "Node.js and npm are required for Docker preparation"
        print_error "Please install Node.js and npm, then run: ./scripts/prepare-docker.sh"
        exit 1
    fi
else
    print_success "Docker build requirements are ready"
fi

# Build and start services
print_status "Building Docker images..."
docker-compose build

if [ $? -eq 0 ]; then
    print_success "Docker images built successfully"
else
    print_error "Failed to build Docker images"
    exit 1
fi

print_status "Starting services..."
docker-compose up -d

if [ $? -eq 0 ]; then
    print_success "Services started successfully"
else
    print_error "Failed to start services"
    exit 1
fi

# Wait for services to be ready
print_status "Waiting for services to be ready..."
sleep 10

# Check service health
print_status "Checking service health..."

# Check API Server
if curl -f http://localhost:8001/health &> /dev/null; then
    print_success "API Server is running on http://localhost:8001"
else
    print_warning "API Server might not be ready yet"
fi

# Check Admin Portal
if curl -f http://localhost:8002 &> /dev/null; then
    print_success "Admin Portal is running on http://localhost:8002"
else
    print_warning "Admin Portal might not be ready yet"
fi

# Check Agent Portal
if curl -f http://localhost:8003 &> /dev/null; then
    print_success "Agent Portal is running on http://localhost:8003"
else
    print_warning "Agent Portal might not be ready yet"
fi

# Check Consumer App
if curl -f http://localhost:8004 &> /dev/null; then
    print_success "Consumer App is running on http://localhost:8004"
else
    print_warning "Consumer App might not be ready yet"
fi

echo ""
print_success "🎉 TravelEase Docker setup completed!"
echo ""
echo "📋 Service URLs:"
echo "   • API Server:     http://localhost:8001"
echo "   • Admin Portal:   http://localhost:8002"
echo "   • Agent Portal:   http://localhost:8003"
echo "   • Consumer App:   http://localhost:8004"
echo ""
echo "🔧 Useful commands:"
echo "   • View logs:      docker-compose logs -f"
echo "   • Stop services:  docker-compose down"
echo "   • Restart:        docker-compose restart"
echo "   • Rebuild:        docker-compose build --no-cache"
echo ""
echo "📖 For testing guide, run: ./scripts/test-guide.sh"
