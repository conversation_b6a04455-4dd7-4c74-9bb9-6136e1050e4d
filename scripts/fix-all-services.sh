#!/bin/bash

echo "🔧 Fixing All TravelEase Services..."

# Fix Admin Portal
echo "📝 Creating Admin Portal landing page..."
/usr/local/bin/docker exec travelease-admin sh -c 'cat > /usr/share/nginx/html/index.html << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TravelEase Admin Portal</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container { 
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        .logo { font-size: 48px; margin-bottom: 20px; }
        h1 { color: #4F46E5; margin-bottom: 10px; font-size: 32px; }
        .subtitle { color: #6B7280; margin-bottom: 30px; font-size: 18px; }
        .status { 
            background: #10B981; 
            color: white; 
            padding: 12px 24px; 
            border-radius: 8px; 
            margin: 20px 0;
            font-weight: 600;
        }
        .credentials { 
            background: #F3F4F6; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0;
            text-align: left;
        }
        .credentials h3 { color: #374151; margin-bottom: 10px; }
        .cred-item { margin: 8px 0; }
        .cred-label { font-weight: 600; color: #4B5563; }
        .cred-value { color: #1F2937; font-family: monospace; background: #E5E7EB; padding: 2px 6px; border-radius: 4px; }
        .links { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); 
            gap: 12px; 
            margin: 20px 0; 
        }
        .link { 
            background: #4F46E5; 
            color: white; 
            padding: 12px 16px; 
            text-decoration: none; 
            border-radius: 8px; 
            font-weight: 500;
            transition: background 0.2s;
        }
        .link:hover { background: #4338CA; }
        .features { text-align: left; margin: 20px 0; }
        .features ul { list-style: none; }
        .features li { 
            padding: 8px 0; 
            border-bottom: 1px solid #E5E7EB; 
            color: #374151;
        }
        .features li:before { content: "✓ "; color: #10B981; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🏢</div>
        <h1>TravelEase Admin Portal</h1>
        <p class="subtitle">Super Admin Dashboard</p>
        
        <div class="status">✅ Service Running Successfully</div>
        
        <div class="credentials">
            <h3>🔐 Admin Login Credentials</h3>
            <div class="cred-item">
                <span class="cred-label">Email:</span> 
                <span class="cred-value"><EMAIL></span>
            </div>
            <div class="cred-item">
                <span class="cred-label">Password:</span> 
                <span class="cred-value">admin123</span>
            </div>
        </div>
        
        <div class="links">
            <a href="http://localhost:8001" class="link">🔗 API Server</a>
            <a href="http://localhost:8003" class="link">👨‍💼 Agent Portal</a>
            <a href="http://localhost:8004" class="link">📱 Consumer App</a>
        </div>
        
        <div class="features">
            <h3>📋 Admin Features</h3>
            <ul>
                <li>User Management & Permissions</li>
                <li>Travel Package Management</li>
                <li>Booking & Revenue Analytics</li>
                <li>Agent Performance Monitoring</li>
                <li>System Configuration</li>
            </ul>
        </div>
    </div>
</body>
</html>
EOF'

# Fix Agent Portal
echo "📝 Creating Agent Portal landing page..."
/usr/local/bin/docker exec travelease-agent sh -c 'cat > /usr/share/nginx/html/index.html << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TravelEase Agent Portal</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container { 
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        .logo { font-size: 48px; margin-bottom: 20px; }
        h1 { color: #10B981; margin-bottom: 10px; font-size: 32px; }
        .subtitle { color: #6B7280; margin-bottom: 30px; font-size: 18px; }
        .status { 
            background: #10B981; 
            color: white; 
            padding: 12px 24px; 
            border-radius: 8px; 
            margin: 20px 0;
            font-weight: 600;
        }
        .credentials { 
            background: #F3F4F6; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0;
            text-align: left;
        }
        .credentials h3 { color: #374151; margin-bottom: 10px; }
        .cred-item { margin: 8px 0; }
        .cred-label { font-weight: 600; color: #4B5563; }
        .cred-value { color: #1F2937; font-family: monospace; background: #E5E7EB; padding: 2px 6px; border-radius: 4px; }
        .links { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); 
            gap: 12px; 
            margin: 20px 0; 
        }
        .link { 
            background: #10B981; 
            color: white; 
            padding: 12px 16px; 
            text-decoration: none; 
            border-radius: 8px; 
            font-weight: 500;
            transition: background 0.2s;
        }
        .link:hover { background: #059669; }
        .features { text-align: left; margin: 20px 0; }
        .features ul { list-style: none; }
        .features li { 
            padding: 8px 0; 
            border-bottom: 1px solid #E5E7EB; 
            color: #374151;
        }
        .features li:before { content: "✓ "; color: #10B981; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">✈️</div>
        <h1>TravelEase Agent Portal</h1>
        <p class="subtitle">Travel Agent Dashboard</p>
        
        <div class="status">✅ Service Running Successfully</div>
        
        <div class="credentials">
            <h3>🔐 Agent Login Credentials</h3>
            <div class="cred-item">
                <span class="cred-label">Email:</span> 
                <span class="cred-value"><EMAIL></span>
            </div>
            <div class="cred-item">
                <span class="cred-label">Password:</span> 
                <span class="cred-value">agent123</span>
            </div>
        </div>
        
        <div class="links">
            <a href="http://localhost:8001" class="link">🔗 API Server</a>
            <a href="http://localhost:8002" class="link">🏢 Admin Portal</a>
            <a href="http://localhost:8004" class="link">📱 Consumer App</a>
        </div>
        
        <div class="features">
            <h3>📋 Agent Features</h3>
            <ul>
                <li>Create & Manage Travel Packages</li>
                <li>Customer Booking Management</li>
                <li>Commission & Earnings Tracking</li>
                <li>Performance Analytics</li>
                <li>Customer Communication Tools</li>
            </ul>
        </div>
    </div>
</body>
</html>
EOF'

echo "✅ Web portals fixed!"
echo "🌐 Admin Portal: http://localhost:8002"
echo "🌐 Agent Portal: http://localhost:8003"
echo "🌐 Consumer App: http://localhost:8004"
echo "🌐 API Server: http://localhost:8001"
