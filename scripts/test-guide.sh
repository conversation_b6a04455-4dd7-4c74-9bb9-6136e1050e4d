#!/bin/bash

# TravelEase Complete Testing Guide
echo "🧪 TravelEase Complete Application Testing Guide"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

print_step() {
    echo -e "${CYAN}Step $1:${NC} $2"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[NOTE]${NC} $1"
}

print_credentials() {
    echo -e "${YELLOW}Credentials:${NC} $1"
}

print_url() {
    echo -e "${CYAN}URL:${NC} $1"
}

# Check if services are running
print_header "Pre-Test Service Check"
print_info "Checking if all services are running..."

services=("8001:API Server" "8002:Admin Portal" "8003:Agent Portal" "8004:Consumer App")
all_running=true

for service in "${services[@]}"; do
    port=$(echo $service | cut -d: -f1)
    name=$(echo $service | cut -d: -f2)
    
    if curl -f http://localhost:$port &> /dev/null; then
        print_success "$name is running on port $port"
    else
        print_warning "$name is NOT running on port $port"
        all_running=false
    fi
done

if [ "$all_running" = false ]; then
    echo -e "\n${RED}Some services are not running. Please run:${NC}"
    echo "  ./scripts/docker-setup.sh"
    echo "  or"
    echo "  docker-compose up -d"
    exit 1
fi

print_header "Complete Application Flow Testing"

print_header "1. API Server Testing"
print_url "http://localhost:8001"
print_step "1.1" "Test API health endpoint"
echo "  curl http://localhost:8001/health"
print_step "1.2" "Test authentication endpoint"
echo "  curl -X POST http://localhost:8001/api/auth/login \\"
echo "    -H 'Content-Type: application/json' \\"
echo "    -d '{\"email\":\"<EMAIL>\",\"password\":\"admin123\"}'"
print_step "1.3" "Test agents endpoint"
echo "  curl http://localhost:8001/api/agents"

print_header "2. Super Admin Portal Testing"
print_url "http://localhost:8002"
print_credentials "Email: <EMAIL> | Password: admin123"
print_step "2.1" "Open Admin Portal in browser"
print_step "2.2" "Login with admin credentials"
print_step "2.3" "Navigate to Dashboard - verify charts and statistics"
print_step "2.4" "Go to Agents section"
print_step "2.5" "Add a new agent (fill all required fields)"
print_step "2.6" "Edit an existing agent"
print_step "2.7" "View agent details"
print_step "2.8" "Test search and filter functionality"
print_step "2.9" "Go to Settings and update configurations"
print_step "2.10" "Logout and verify redirect to login"

print_header "3. Travel Agent Portal Testing"
print_url "http://localhost:8003"
print_credentials "Email: <EMAIL> | Password: agent123"
print_step "3.1" "Open Agent Portal in browser"
print_step "3.2" "Login with agent credentials"
print_step "3.3" "Navigate to Dashboard - verify metrics and charts"
print_step "3.4" "Go to Packages section"
print_step "3.5" "Create a new travel package:"
echo "    - Add package details (name, description, price)"
echo "    - Upload package images"
echo "    - Add itinerary items"
echo "    - Set availability dates"
print_step "3.6" "Edit an existing package"
print_step "3.7" "Go to Customers section"
print_step "3.8" "Add a new customer profile"
print_step "3.9" "View customer details and booking history"
print_step "3.10" "Go to Bookings section"
print_step "3.11" "Create a new booking for a customer"
print_step "3.12" "Update booking status"
print_step "3.13" "Go to Itinerary Builder"
print_step "3.14" "Create a detailed itinerary with multiple days"
print_step "3.15" "Go to Notifications"
print_step "3.16" "Send a notification to a customer"
print_step "3.17" "Logout and verify redirect"

print_header "4. Consumer Mobile App Testing"
print_url "http://localhost:8004"
print_credentials "Email: <EMAIL> | Password: customer123"
print_step "4.1" "Open Consumer App in browser"
print_step "4.2" "Test login with customer credentials"
print_step "4.3" "Navigate to Trip Dashboard"
print_step "4.4" "Verify trip cards display correctly with status badges"
print_step "4.5" "Click on a trip to view details"
print_step "4.6" "Test trip details page:"
echo "    - View image gallery"
echo "    - Switch between Highlights and Itinerary tabs"
echo "    - Verify pickup information"
echo "    - Check timeline view"
print_step "4.7" "Go to Profile section"
print_step "4.8" "Update profile information"
print_step "4.9" "Go to Notifications"
print_step "4.10" "Mark notifications as read"
print_step "4.11" "Test search functionality"
print_step "4.12" "Test trip filtering"
print_step "4.13" "Logout and verify redirect"

print_header "5. Cross-Application Integration Testing"
print_step "5.1" "Create a package in Agent Portal"
print_step "5.2" "Create a customer in Agent Portal"
print_step "5.3" "Create a booking for the customer"
print_step "5.4" "Send a notification from Agent Portal"
print_step "5.5" "Login to Consumer App with customer credentials"
print_step "5.6" "Verify the booking appears in trip dashboard"
print_step "5.7" "Verify notification appears in notifications"
print_step "5.8" "Check trip details match the package created"

print_header "6. Data Flow Testing"
print_step "6.1" "Admin creates/edits an agent in Admin Portal"
print_step "6.2" "Agent logs into Agent Portal with new credentials"
print_step "6.3" "Agent creates packages and manages customers"
print_step "6.4" "Customer sees updates in Consumer App"
print_step "6.5" "Verify data consistency across all applications"

print_header "7. Error Handling Testing"
print_step "7.1" "Test invalid login credentials on all portals"
print_step "7.2" "Test form validation (empty fields, invalid formats)"
print_step "7.3" "Test network error handling (stop API server temporarily)"
print_step "7.4" "Test unauthorized access (access protected routes without login)"

print_header "8. Performance Testing"
print_step "8.1" "Test page load times"
print_step "8.2" "Test with multiple browser tabs open"
print_step "8.3" "Test image upload functionality"
print_step "8.4" "Test search with large datasets"

print_header "9. Mobile Responsiveness Testing"
print_step "9.1" "Test Consumer App on different screen sizes"
print_step "9.2" "Test touch interactions"
print_step "9.3" "Test mobile navigation"

print_header "10. Browser Compatibility Testing"
print_step "10.1" "Test on Chrome, Firefox, Safari, Edge"
print_step "10.2" "Test JavaScript functionality"
print_step "10.3" "Test CSS rendering"

echo -e "\n${GREEN}=== Testing Checklist ===${NC}"
echo "□ API Server responds to all endpoints"
echo "□ Admin Portal login and CRUD operations work"
echo "□ Agent Portal full workflow completed"
echo "□ Consumer App displays data correctly"
echo "□ Cross-application data flow verified"
echo "□ Error handling works as expected"
echo "□ Performance is acceptable"
echo "□ Mobile responsiveness verified"
echo "□ Browser compatibility confirmed"

echo -e "\n${PURPLE}=== Troubleshooting ===${NC}"
echo "If you encounter issues:"
echo "1. Check service logs: docker-compose logs -f [service-name]"
echo "2. Restart services: docker-compose restart"
echo "3. Rebuild images: docker-compose build --no-cache"
echo "4. Check network connectivity between containers"
echo "5. Verify environment variables in .env file"

echo -e "\n${GREEN}Happy Testing! 🚀${NC}"
