#!/bin/bash

# TravelEase Testing Without Docker
echo "🧪 TravelEase Testing (Without Docker)"
echo "======================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ PASS:${NC} $1"
}

print_fail() {
    echo -e "${RED}✗ FAIL:${NC} $1"
}

print_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if eval "$test_command" &> /dev/null; then
        print_success "$test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        print_fail "$test_name"
        return 1
    fi
}

print_header "Project Structure Validation"

# Check main directories
run_test "API Server directory exists" "[ -d 'api-server' ]"
run_test "Admin Portal directory exists" "[ -d 'admin-portal' ]"
run_test "Agent Portal directory exists" "[ -d 'agent-portal' ]"
run_test "Consumer App directory exists" "[ -d 'consumer-app' ]"
run_test "Shared directory exists" "[ -d 'shared' ]"
run_test "Scripts directory exists" "[ -d 'scripts' ]"

print_header "Package Configuration Validation"

# Check package.json files
run_test "API Server package.json exists" "[ -f 'api-server/package.json' ]"
run_test "Admin Portal package.json exists" "[ -f 'admin-portal/package.json' ]"
run_test "Agent Portal package.json exists" "[ -f 'agent-portal/package.json' ]"
run_test "Consumer App package.json exists" "[ -f 'consumer-app/package.json' ]"

# Check key dependencies
run_test "API Server has Express" "grep -q 'express' api-server/package.json"
run_test "Admin Portal has React" "grep -q 'react' admin-portal/package.json"
run_test "Agent Portal has React" "grep -q 'react' agent-portal/package.json"
run_test "Consumer App has Expo" "grep -q 'expo' consumer-app/package.json"

print_header "Docker Configuration Validation"

# Check Docker files
run_test "Main docker-compose.yml exists" "[ -f 'docker-compose.yml' ]"
run_test "Dev docker-compose.yml exists" "[ -f 'docker-compose.dev.yml' ]"
run_test "API Server Dockerfile exists" "[ -f 'api-server/Dockerfile' ]"
run_test "Admin Portal Dockerfile exists" "[ -f 'admin-portal/Dockerfile' ]"
run_test "Agent Portal Dockerfile exists" "[ -f 'agent-portal/Dockerfile' ]"
run_test "Consumer App Dockerfile exists" "[ -f 'consumer-app/Dockerfile' ]"

# Check Docker ignore files
run_test "API Server .dockerignore exists" "[ -f 'api-server/.dockerignore' ]"
run_test "Admin Portal .dockerignore exists" "[ -f 'admin-portal/.dockerignore' ]"
run_test "Agent Portal .dockerignore exists" "[ -f 'agent-portal/.dockerignore' ]"
run_test "Consumer App .dockerignore exists" "[ -f 'consumer-app/.dockerignore' ]"

print_header "Environment Configuration Validation"

# Check environment files
run_test "Root .env.example exists" "[ -f '.env.example' ]"
run_test "API Server .env exists" "[ -f 'api-server/.env' ]"
run_test "Admin Portal .env exists" "[ -f 'admin-portal/.env' ]"
run_test "Agent Portal .env exists" "[ -f 'agent-portal/.env' ]"
run_test "Consumer App .env exists" "[ -f 'consumer-app/.env' ]"

# Check API URL configurations
if [ -f "admin-portal/.env" ]; then
    if grep -q "8001" admin-portal/.env; then
        print_success "Admin Portal API URL configured correctly"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_fail "Admin Portal API URL not configured for port 8001"
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

if [ -f "agent-portal/.env" ]; then
    if grep -q "8001" agent-portal/.env; then
        print_success "Agent Portal API URL configured correctly"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_fail "Agent Portal API URL not configured for port 8001"
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

if [ -f "consumer-app/.env" ]; then
    if grep -q "8001" consumer-app/.env; then
        print_success "Consumer App API URL configured correctly"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_fail "Consumer App API URL not configured for port 8001"
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
fi

print_header "Script Validation"

# Check scripts exist and are executable
run_test "docker-setup.sh exists" "[ -f 'scripts/docker-setup.sh' ]"
run_test "docker-setup.sh is executable" "[ -x 'scripts/docker-setup.sh' ]"
run_test "docker-dev.sh exists" "[ -f 'scripts/docker-dev.sh' ]"
run_test "docker-dev.sh is executable" "[ -x 'scripts/docker-dev.sh' ]"
run_test "integration-test.sh exists" "[ -f 'scripts/integration-test.sh' ]"
run_test "integration-test.sh is executable" "[ -x 'scripts/integration-test.sh' ]"
run_test "verify-setup.sh exists" "[ -f 'scripts/verify-setup.sh' ]"
run_test "verify-setup.sh is executable" "[ -x 'scripts/verify-setup.sh' ]"

print_header "Source Code Validation"

# Check key source files exist
run_test "API Server main file exists" "[ -f 'api-server/src/index.ts' ]"
run_test "Admin Portal App.tsx exists" "[ -f 'admin-portal/src/App.tsx' ]"
run_test "Agent Portal App.tsx exists" "[ -f 'agent-portal/src/App.tsx' ]"
run_test "Consumer App App.tsx exists" "[ -f 'consumer-app/App.tsx' ]"

# Check API routes
run_test "Auth routes exist" "[ -f 'api-server/src/routes/auth.ts' ]"
run_test "Agent routes exist" "[ -f 'api-server/src/routes/agents.ts' ]"
run_test "Package routes exist" "[ -f 'api-server/src/routes/packages.ts' ]"
run_test "Customer routes exist" "[ -f 'api-server/src/routes/customers.ts' ]"
run_test "Booking routes exist" "[ -f 'api-server/src/routes/bookings.ts' ]"

print_header "Documentation Validation"

# Check documentation files
run_test "Main README exists" "[ -f 'README.md' ]"
run_test "Docker setup guide exists" "[ -f 'DOCKER_SETUP.md' ]"
run_test "Testing guide exists" "[ -f 'TESTING_GUIDE.md' ]"
run_test "Integration summary exists" "[ -f 'INTEGRATION_SUMMARY.md' ]"

print_header "Docker Compose Syntax Check"

# Basic syntax validation for docker-compose files
if command -v docker-compose &> /dev/null; then
    if docker-compose config &> /dev/null; then
        print_success "Docker Compose syntax is valid"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_fail "Docker Compose syntax has errors"
    fi
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
else
    print_info "Docker Compose not available - skipping syntax check"
fi

print_header "Node.js Dependencies Check"

# Check if Node.js is available
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_success "Node.js is available: $NODE_VERSION"
    
    if command -v npm &> /dev/null; then
        NPM_VERSION=$(npm --version)
        print_success "npm is available: $NPM_VERSION"
        
        # Check if dependencies are installed
        if [ -d "api-server/node_modules" ]; then
            print_success "API Server dependencies are installed"
        else
            print_info "API Server dependencies not installed. Run: cd api-server && npm install"
        fi
        
        if [ -d "admin-portal/node_modules" ]; then
            print_success "Admin Portal dependencies are installed"
        else
            print_info "Admin Portal dependencies not installed. Run: cd admin-portal && npm install"
        fi
        
        if [ -d "agent-portal/node_modules" ]; then
            print_success "Agent Portal dependencies are installed"
        else
            print_info "Agent Portal dependencies not installed. Run: cd agent-portal && npm install"
        fi
        
        if [ -d "consumer-app/node_modules" ]; then
            print_success "Consumer App dependencies are installed"
        else
            print_info "Consumer App dependencies not installed. Run: cd consumer-app && npm install"
        fi
    else
        print_info "npm not available"
    fi
else
    print_info "Node.js not available"
fi

print_header "Test Summary"

FAILED_TESTS=$((TOTAL_TESTS - PASSED_TESTS))

echo -e "\n${BLUE}Test Results:${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed!${NC}"
    echo -e "${GREEN}✅ TravelEase project structure and configuration are valid.${NC}"
else
    echo -e "\n${YELLOW}⚠️  Some tests failed. Please review the issues above.${NC}"
fi

echo -e "\n${BLUE}Next Steps:${NC}"
echo -e "1. Install Docker and Docker Compose"
echo -e "2. Run: ./scripts/docker-setup.sh"
echo -e "3. Test with: ./scripts/quick-test.sh"
echo -e "4. Full testing: ./scripts/test-guide.sh"

echo -e "\n${BLUE}Manual Testing (without Docker):${NC}"
echo -e "1. Install dependencies: npm run install:all"
echo -e "2. Start API server: cd api-server && npm run dev"
echo -e "3. Start admin portal: cd admin-portal && npm start"
echo -e "4. Start agent portal: cd agent-portal && npm start"
echo -e "5. Start consumer app: cd consumer-app && npm start"

exit $FAILED_TESTS
