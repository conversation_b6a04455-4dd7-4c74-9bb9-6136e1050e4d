#!/bin/bash

# TravelEase Docker Preparation Script
echo "🔧 Preparing TravelEase for Docker Build"
echo "========================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "Node.js and npm are available ✓"

# Function to generate package-lock.json
generate_package_lock() {
    local dir=$1
    local name=$2
    
    print_status "Generating package-lock.json for $name..."
    
    if [ -d "$dir" ]; then
        cd "$dir"
        
        # Remove existing node_modules and package-lock.json
        rm -rf node_modules package-lock.json
        
        # Install dependencies to generate package-lock.json
        if npm install &> /dev/null; then
            print_success "Generated package-lock.json for $name"
        else
            print_error "Failed to generate package-lock.json for $name"
            cd ..
            return 1
        fi
        
        cd ..
    else
        print_error "Directory $dir not found"
        return 1
    fi
}

# Generate package-lock.json for all applications
print_status "Generating package-lock.json files for Docker builds..."

generate_package_lock "api-server" "API Server"
generate_package_lock "admin-portal" "Admin Portal"
generate_package_lock "agent-portal" "Agent Portal"
generate_package_lock "consumer-app" "Consumer App"

# Update Dockerfiles to use npm ci instead of npm install
print_status "Updating Dockerfiles to use npm ci..."

# Update API Server Dockerfile
if [ -f "api-server/Dockerfile" ]; then
    sed -i.bak 's/npm install --only=production/npm ci --only=production/g' api-server/Dockerfile
    print_success "Updated API Server Dockerfile"
fi

# Update Admin Portal Dockerfile
if [ -f "admin-portal/Dockerfile" ]; then
    sed -i.bak 's/npm install/npm ci/g' admin-portal/Dockerfile
    print_success "Updated Admin Portal Dockerfile"
fi

# Update Agent Portal Dockerfile
if [ -f "agent-portal/Dockerfile" ]; then
    sed -i.bak 's/npm install/npm ci/g' agent-portal/Dockerfile
    print_success "Updated Agent Portal Dockerfile"
fi

# Update Consumer App Dockerfile
if [ -f "consumer-app/Dockerfile" ]; then
    sed -i.bak 's/npm install/npm ci/g' consumer-app/Dockerfile
    print_success "Updated Consumer App Dockerfile"
fi

# Clean up backup files
rm -f api-server/Dockerfile.bak admin-portal/Dockerfile.bak agent-portal/Dockerfile.bak consumer-app/Dockerfile.bak

# Verify package-lock.json files exist
print_status "Verifying package-lock.json files..."

check_package_lock() {
    local dir=$1
    local name=$2
    
    if [ -f "$dir/package-lock.json" ]; then
        print_success "$name package-lock.json exists"
        return 0
    else
        print_error "$name package-lock.json missing"
        return 1
    fi
}

ALL_GOOD=true

check_package_lock "api-server" "API Server" || ALL_GOOD=false
check_package_lock "admin-portal" "Admin Portal" || ALL_GOOD=false
check_package_lock "agent-portal" "Agent Portal" || ALL_GOOD=false
check_package_lock "consumer-app" "Consumer App" || ALL_GOOD=false

if [ "$ALL_GOOD" = true ]; then
    print_success "All package-lock.json files are ready!"
    echo ""
    print_status "Docker build is now ready. You can run:"
    echo "  ./scripts/docker-setup.sh"
    echo "  or"
    echo "  docker-compose build"
else
    print_error "Some package-lock.json files are missing. Please check the errors above."
    exit 1
fi

# Create .dockerignore files if they don't exist
print_status "Checking .dockerignore files..."

create_dockerignore() {
    local dir=$1
    local name=$2
    
    if [ ! -f "$dir/.dockerignore" ]; then
        print_status "Creating .dockerignore for $name..."
        cat > "$dir/.dockerignore" << EOF
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.git
.gitignore
README.md
.env
.nyc_output
coverage
.DS_Store
*.log
EOF
        print_success "Created .dockerignore for $name"
    else
        print_success "$name .dockerignore already exists"
    fi
}

create_dockerignore "api-server" "API Server"
create_dockerignore "admin-portal" "Admin Portal"
create_dockerignore "agent-portal" "Agent Portal"
create_dockerignore "consumer-app" "Consumer App"

echo ""
print_success "🎉 Docker preparation completed!"
echo ""
print_status "Next steps:"
echo "1. Run: ./scripts/docker-setup.sh"
echo "2. Test: ./scripts/quick-test.sh"
echo "3. Full testing: ./scripts/test-guide.sh"
