# TravelEase - Complete Travel Management Platform

A comprehensive travel management solution with separate portals for super admins, travel agents, and consumers.

## Project Structure

```
travelease/
├── admin-portal/          # Super Admin Web App (React + TypeScript)
├── agent-portal/          # Travel Agent Web App (React + TypeScript)  
├── consumer-app/          # Consumer Mobile App (React Native + TypeScript)
├── api-server/            # Mock API Backend (Node.js + Express)
├── shared/                # Shared components & data
├── package.json           # Root package.json with workspace scripts
└── README.md             # This file
```

## Technology Stack

- **Web Portals**: React + TypeScript + Tailwind CSS
- **Mobile App**: React Native + TypeScript
- **Backend**: Node.js + Express + TypeScript
- **Mock Data**: JSON files
- **Routing**: React Router (web) / React Navigation (mobile)
- **State**: React Context API
- **Package Manager**: npm

## Quick Start

### Install All Dependencies
```bash
npm run install:all
```

### Development Mode
```bash
# Start API server (runs on port 3001)
npm run dev:api

# Start Admin Portal (runs on port 3000)
npm run dev:admin

# Start Agent Portal (runs on port 3002)  
npm run dev:agent

# Start Consumer Mobile App
npm run dev:consumer
```

### Build All Applications
```bash
npm run build:all
```

## 🐳 Docker Setup (Recommended)

For easy installation and maintenance with custom ports to avoid conflicts:

### Prerequisites
- Docker (v20.10+)
- Docker Compose (v2.0+)

### One-Command Setup
```bash
git clone <repository-url>
cd travelease
./scripts/docker-setup.sh
```

### Access Applications (Custom Ports)
- **API Server**: http://localhost:8001
- **Admin Portal**: http://localhost:8002
- **Agent Portal**: http://localhost:8003
- **Consumer App**: http://localhost:8004

### Development with Hot Reload
```bash
./scripts/docker-dev.sh
```

### Complete Testing Guide
```bash
./scripts/test-guide.sh
```

### Docker Commands
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down

# Restart services
docker-compose restart

# Rebuild images
docker-compose build --no-cache
```

For detailed Docker setup instructions, see [DOCKER_SETUP.md](DOCKER_SETUP.md)

### Docker Troubleshooting
If you encounter Docker build issues:
```bash
# Prepare Docker build requirements
./scripts/prepare-docker.sh

# Then run setup
./scripts/docker-setup.sh
```

For detailed troubleshooting, see [DOCKER_TROUBLESHOOTING.md](DOCKER_TROUBLESHOOTING.md)

## 🧪 Manual Testing (Without Docker)

If Docker is not available, you can test manually:

### Quick Validation
```bash
./scripts/test-without-docker.sh
```

### Manual Setup
```bash
# Install dependencies
npm run install:all

# Start services (4 terminals)
cd api-server && npm run dev          # Terminal 1
cd admin-portal && npm start          # Terminal 2
cd agent-portal && PORT=3002 npm start # Terminal 3
cd consumer-app && npm start          # Terminal 4
```

### Access Applications
- **API Server**: http://localhost:8001
- **Admin Portal**: http://localhost:3000
- **Agent Portal**: http://localhost:3002
- **Consumer App**: http://localhost:19006

For detailed manual testing guide, see [MANUAL_TESTING_GUIDE.md](MANUAL_TESTING_GUIDE.md)

## Application Features

### Super Admin Portal (Port 3000)
- Dashboard with system overview
- Travel agent management (CRUD)
- System settings and configurations

### Travel Agent Portal (Port 3002)
- Agent dashboard with metrics
- Travel package management
- Customer management
- Itinerary builder
- Booking management
- Customer notifications

### Consumer Mobile App
- Trip dashboard
- Detailed trip itineraries
- User profile management
- Notifications from travel agents

### Mock API Server (Port 3001)
- RESTful endpoints for all CRUD operations
- JWT-based authentication
- Mock data serving
- CORS enabled for frontend access

## Authentication

### Default Users
- **Super Admin**: <EMAIL> / admin123
- **Travel Agent**: <EMAIL> / agent123
- **Consumer**: <EMAIL> / user123

## Development Notes

- Each application is a separate npm workspace
- Mock data is stored in JSON files in the api-server
- All applications use TypeScript for type safety
- Responsive design for web portals
- Professional mobile UI with glass morphism effects
