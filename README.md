
I have addressed the CORS policy error and the WebSocket connection failure.

**CORS Policy Error Fix:**
- Updated the `origin` list in `api-server/src/index.ts` to include the correct external ports for the `admin-portal` (`http://localhost:8002`) and `agent-portal` (`http://localhost:8003`). This ensures that the API server allows requests from these origins.

**WebSocket Connection Failure Fix:**
- Added `WDS_SOCKET_HOST=localhost` and `WDS_SOCKET_PORT` environment variables to the `Dockerfile`s of `admin-portal` (port `8002`) and `agent-portal` (port `8003`). This configures the Webpack Dev Server to correctly establish WebSocket connections for hot module replacement (HMR) to the exposed ports of the React applications when running in Docker.

These changes should resolve the reported network communication issues.
