{"name": "travelease-api-server", "version": "1.0.0", "description": "Mock API backend for TravelEase application", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "test": "jest", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "keywords": ["travel", "api", "mock", "backend"], "author": "TravelEase Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "multer": "^1.4.5-lts.1", "uuid": "^9.0.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/multer": "^1.4.7", "@types/uuid": "^9.0.2", "@types/node": "^20.4.5", "@types/jest": "^29.5.3", "typescript": "^5.1.6", "ts-node-dev": "^2.0.0", "jest": "^29.6.1", "ts-jest": "^29.1.1", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "rimraf": "^5.0.1"}}