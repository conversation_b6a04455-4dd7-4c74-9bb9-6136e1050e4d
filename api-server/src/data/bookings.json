[{"id": "booking-1", "packageId": "pkg-1", "consumerId": "consumer-1", "agentId": "agent-1", "status": "confirmed", "bookingDate": "2024-03-10T00:00:00.000Z", "travelDate": "2024-04-15T00:00:00.000Z", "numberOfTravelers": 2, "totalAmount": 4998, "paidAmount": 2499, "paymentStatus": "partial", "specialRequests": "Vegetarian meals for both travelers", "travelers": [{"id": "traveler-1", "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "1990-05-15", "passportNumber": "US123456789", "nationality": "American", "dietaryRestrictions": ["vegetarian"], "medicalConditions": []}, {"id": "traveler-2", "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "1988-08-22", "passportNumber": "US987654321", "nationality": "American", "dietaryRestrictions": ["vegetarian"], "medicalConditions": []}], "createdAt": "2024-03-10T00:00:00.000Z", "updatedAt": "2024-03-12T00:00:00.000Z"}, {"id": "booking-2", "packageId": "pkg-2", "consumerId": "consumer-2", "agentId": "agent-2", "status": "pending", "bookingDate": "2024-03-20T00:00:00.000Z", "travelDate": "2024-05-01T00:00:00.000Z", "numberOfTravelers": 1, "totalAmount": 1299, "paidAmount": 0, "paymentStatus": "pending", "specialRequests": "Single room accommodation", "travelers": [{"id": "traveler-3", "firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "1985-08-22", "passportNumber": "US555666777", "nationality": "American", "dietaryRestrictions": [], "medicalConditions": []}], "createdAt": "2024-03-20T00:00:00.000Z", "updatedAt": "2024-03-20T00:00:00.000Z"}]