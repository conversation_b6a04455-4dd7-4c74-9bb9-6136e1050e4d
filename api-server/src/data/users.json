[{"id": "admin-1", "email": "<EMAIL>", "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", "role": "super_admin", "firstName": "Super", "lastName": "Admin", "phone": "******-0001", "avatar": "/uploads/avatars/admin.jpg", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "permissions": ["manage_agents", "view_analytics", "system_settings"]}, {"id": "agent-1", "email": "<EMAIL>", "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", "role": "travel_agent", "firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "******-0002", "avatar": "/uploads/avatars/agent1.jpg", "createdAt": "2024-01-15T00:00:00.000Z", "updatedAt": "2024-01-15T00:00:00.000Z", "agencyName": "Adventure Travel Co.", "licenseNumber": "LIC-2024-001", "specializations": ["Adventure Travel", "Cultural Tours", "Family Vacations"], "rating": 4.8, "totalBookings": 156, "revenue": 245000, "isActive": true}, {"id": "agent-2", "email": "<EMAIL>", "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", "role": "travel_agent", "firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "******-0003", "avatar": "/uploads/avatars/agent2.jpg", "createdAt": "2024-02-01T00:00:00.000Z", "updatedAt": "2024-02-01T00:00:00.000Z", "agencyName": "Luxury Escapes", "licenseNumber": "LIC-2024-002", "specializations": ["Luxury Travel", "Honeymoon Packages", "Business Travel"], "rating": 4.9, "totalBookings": 89, "revenue": 180000, "isActive": true}, {"id": "consumer-1", "email": "<EMAIL>", "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", "role": "consumer", "firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "******-0004", "avatar": "/uploads/avatars/user1.jpg", "createdAt": "2024-03-01T00:00:00.000Z", "updatedAt": "2024-03-01T00:00:00.000Z", "dateOfBirth": "1990-05-15", "address": {"street": "123 Main St", "city": "New York", "state": "NY", "country": "USA", "zipCode": "10001"}, "emergencyContact": {"name": "<PERSON>", "relationship": "Spouse", "phone": "******-0005", "email": "micha<PERSON>.<EMAIL>"}, "preferences": {"budgetRange": "mid-range", "travelStyle": "cultural", "accommodationType": "hotel", "dietaryRestrictions": ["vegetarian"]}}, {"id": "consumer-2", "email": "<EMAIL>", "password": "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", "role": "consumer", "firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "******-0006", "avatar": "/uploads/avatars/user2.jpg", "createdAt": "2024-03-15T00:00:00.000Z", "updatedAt": "2024-03-15T00:00:00.000Z", "dateOfBirth": "1985-08-22", "address": {"street": "456 Oak Ave", "city": "Los Angeles", "state": "CA", "country": "USA", "zipCode": "90210"}, "emergencyContact": {"name": "<PERSON>", "relationship": "Sister", "phone": "******-0007", "email": "<EMAIL>"}, "preferences": {"budgetRange": "luxury", "travelStyle": "adventure", "accommodationType": "resort", "dietaryRestrictions": []}}]