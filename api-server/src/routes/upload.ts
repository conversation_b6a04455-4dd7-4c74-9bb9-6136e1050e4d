import express from 'express';
import multer from 'multer';
import path from 'path';
import { authenticateToken } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req: any, file: any, cb: any) => {
  // Check file type
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  }
});

// Single file upload
router.post('/single', authenticateToken, upload.single('file'), (req, res, next) => {
  try {
    if (!req.file) {
      return next(createError('No file uploaded', 400));
    }

    const fileUrl = `/uploads/${req.file.filename}`;

    res.json({
      success: true,
      data: {
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype,
        url: fileUrl,
      },
      message: 'File uploaded successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Multiple files upload
router.post('/multiple', authenticateToken, upload.array('files', 5), (req, res, next) => {
  try {
    if (!req.files || (req.files as Express.Multer.File[]).length === 0) {
      return next(createError('No files uploaded', 400));
    }

    const files = (req.files as Express.Multer.File[]).map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
      url: `/uploads/${file.filename}`,
    }));

    res.json({
      success: true,
      data: {
        files,
        count: files.length,
      },
      message: `${files.length} files uploaded successfully`,
    });
  } catch (error) {
    next(error);
  }
});

// Avatar upload
router.post('/avatar', authenticateToken, upload.single('avatar'), (req, res, next) => {
  try {
    if (!req.file) {
      return next(createError('No avatar file uploaded', 400));
    }

    const avatarUrl = `/uploads/avatars/${req.file.filename}`;

    // In a real app, you would update the user's avatar URL in the database
    res.json({
      success: true,
      data: {
        avatarUrl,
        filename: req.file.filename,
        size: req.file.size,
      },
      message: 'Avatar uploaded successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Package images upload
router.post('/package-images', authenticateToken, upload.array('images', 10), (req, res, next) => {
  try {
    if (!req.files || (req.files as Express.Multer.File[]).length === 0) {
      return next(createError('No images uploaded', 400));
    }

    const images = (req.files as Express.Multer.File[]).map(file => ({
      filename: file.filename,
      originalName: file.originalname,
      size: file.size,
      url: `/uploads/packages/${file.filename}`,
    }));

    res.json({
      success: true,
      data: {
        images,
        count: images.length,
      },
      message: `${images.length} package images uploaded successfully`,
    });
  } catch (error) {
    next(error);
  }
});

// Error handling for multer
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large. Maximum size is 5MB.',
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Too many files. Maximum is 10 files.',
      });
    }
  }
  
  if (error.message === 'Only image files are allowed!') {
    return res.status(400).json({
      success: false,
      error: 'Only image files are allowed.',
    });
  }

  next(error);
});

export default router;
