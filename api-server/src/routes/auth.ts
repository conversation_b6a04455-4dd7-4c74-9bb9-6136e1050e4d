import express from 'express';
import bcrypt from 'bcryptjs';
import { generateToken } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import usersData from '../data/users.json';

const router = express.Router();

// Login endpoint
router.post('/login', async (req, res, next) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return next(createError('Email and password are required', 400));
    }

    // Find user by email
    const user = usersData.find(u => u.email === email);
    if (!user) {
      return next(createError('Invalid credentials', 401));
    }

    // For demo purposes, we'll accept the plain text passwords from constants
    // In production, you would compare with hashed passwords
    const validPasswords = {
      '<EMAIL>': 'admin123',
      '<EMAIL>': 'agent123',
      '<EMAIL>': 'user123',
      '<EMAIL>': 'agent123',
      '<EMAIL>': 'user123'
    };

    const isValidPassword = validPasswords[email as keyof typeof validPasswords] === password;
    
    if (!isValidPassword) {
      return next(createError('Invalid credentials', 401));
    }

    // Generate JWT token
    const token = generateToken({
      id: user.id,
      email: user.email,
      role: user.role,
    });

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: {
        user: userWithoutPassword,
        token,
        expiresIn: 86400, // 24 hours in seconds
      },
      message: 'Login successful',
    });
  } catch (error) {
    next(error);
  }
});

// Logout endpoint (client-side token removal)
router.post('/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logout successful',
  });
});

// Token refresh endpoint
router.post('/refresh', (req, res, next) => {
  try {
    const { token } = req.body;

    if (!token) {
      return next(createError('Token is required', 400));
    }

    // In a real app, you would verify the old token and issue a new one
    // For demo purposes, we'll just return the same token
    res.json({
      success: true,
      data: {
        token,
        expiresIn: 86400,
      },
      message: 'Token refreshed successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
