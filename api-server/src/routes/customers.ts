import express from 'express';
import { authenticateToken, authorizeR<PERSON>s, AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import usersData from '../data/users.json';

const router = express.Router();

// Get current user profile
router.get('/profile', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const user = usersData.find(u => u.id === req.user?.id);
    
    if (!user) {
      return next(createError('User not found', 404));
    }

    const { password, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: userWithoutPassword,
    });
  } catch (error) {
    next(error);
  }
});

// Update current user profile
router.put('/profile', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const userIndex = usersData.findIndex(u => u.id === req.user?.id);
    
    if (userIndex === -1) {
      return next(createError('User not found', 404));
    }

    const { password, role, id, ...updateData } = req.body;
    
    // Update user data (in a real app, this would update the database)
    const updatedUser = {
      ...usersData[userIndex],
      ...updateData,
      updatedAt: new Date().toISOString(),
    };

    // Remove password from response
    const { password: _, ...userWithoutPassword } = updatedUser;

    res.json({
      success: true,
      data: userWithoutPassword,
      message: 'Profile updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Get all users (admin only)
router.get('/', authenticateToken, authorizeRoles('super_admin'), (req, res, next) => {
  try {
    const { role, page = 1, limit = 10 } = req.query;
    
    let filteredUsers = usersData;
    
    // Filter by role if specified
    if (role) {
      filteredUsers = usersData.filter(u => u.role === role);
    }

    // Pagination
    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    // Remove passwords from response
    const usersWithoutPasswords = paginatedUsers.map(({ password, ...user }) => user);

    res.json({
      success: true,
      data: {
        users: usersWithoutPasswords,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: filteredUsers.length,
          pages: Math.ceil(filteredUsers.length / Number(limit)),
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

// Get user by ID (admin only)
router.get('/:id', authenticateToken, authorizeRoles('super_admin'), (req, res, next) => {
  try {
    const { id } = req.params;
    const user = usersData.find(u => u.id === id);
    
    if (!user) {
      return next(createError('User not found', 404));
    }

    const { password, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: userWithoutPassword,
    });
  } catch (error) {
    next(error);
  }
});

// Create new user (admin only)
router.post('/', authenticateToken, authorizeRoles('super_admin'), (req, res, next) => {
  try {
    const userData = req.body;
    
    // Check if email already exists
    const existingUser = usersData.find(u => u.email === userData.email);
    if (existingUser) {
      return next(createError('Email already exists', 400));
    }

    const newUser = {
      id: `user-${Date.now()}`,
      ...userData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // In a real app, this would save to database
    const { password, ...userWithoutPassword } = newUser;

    res.status(201).json({
      success: true,
      data: userWithoutPassword,
      message: 'User created successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Update user (admin only)
router.put('/:id', authenticateToken, authorizeRoles('super_admin'), (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const userIndex = usersData.findIndex(u => u.id === id);
    if (userIndex === -1) {
      return next(createError('User not found', 404));
    }

    const updatedUser = {
      ...usersData[userIndex],
      ...updateData,
      updatedAt: new Date().toISOString(),
    };

    // In a real app, this would update the database
    const { password, ...userWithoutPassword } = updatedUser;

    res.json({
      success: true,
      data: userWithoutPassword,
      message: 'User updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Delete user (admin only)
router.delete('/:id', authenticateToken, authorizeRoles('super_admin'), (req, res, next) => {
  try {
    const { id } = req.params;
    
    const userIndex = usersData.findIndex(u => u.id === id);
    if (userIndex === -1) {
      return next(createError('User not found', 404));
    }

    // In a real app, this would delete from database
    res.json({
      success: true,
      message: 'User deleted successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
