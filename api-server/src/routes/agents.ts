import express from 'express';
import { authenticateToken, authorizeRoles, AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import usersData from '../data/users.json';
import bookingsData from '../data/bookings.json';

const router = express.Router();

// Get all agents (admin only)
router.get('/', authenticateToken, authorizeRoles('super_admin'), (req, res, next) => {
  try {
    const { page = 1, limit = 10, search, status } = req.query;
    
    let agents = usersData.filter(u => u.role === 'travel_agent');
    
    // Search filter
    if (search) {
      const searchTerm = String(search).toLowerCase();
      agents = agents.filter(agent => 
        agent.firstName.toLowerCase().includes(searchTerm) ||
        agent.lastName.toLowerCase().includes(searchTerm) ||
        agent.email.toLowerCase().includes(searchTerm) ||
        (agent as any).agencyName?.toLowerCase().includes(searchTerm)
      );
    }

    // Status filter
    if (status) {
      agents = agents.filter(agent => 
        status === 'active' ? (agent as any).isActive : !(agent as any).isActive
      );
    }

    // Pagination
    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedAgents = agents.slice(startIndex, endIndex);

    // Remove passwords and add stats
    const agentsWithStats = paginatedAgents.map(agent => {
      const { password, ...agentData } = agent;
      const agentBookings = bookingsData.filter(b => b.agentId === agent.id);
      
      return {
        ...agentData,
        stats: {
          totalBookings: agentBookings.length,
          activeBookings: agentBookings.filter(b => b.status === 'confirmed').length,
          revenue: agentBookings.reduce((sum, b) => sum + b.totalAmount, 0),
        }
      };
    });

    res.json({
      success: true,
      data: {
        agents: agentsWithStats,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: agents.length,
          pages: Math.ceil(agents.length / Number(limit)),
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

// Get agent by ID
router.get('/:id', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const agent = usersData.find(u => u.id === id && u.role === 'travel_agent');
    
    if (!agent) {
      return next(createError('Agent not found', 404));
    }

    // Check permissions
    if (req.user?.role !== 'super_admin' && req.user?.id !== id) {
      return next(createError('Access denied', 403));
    }

    const { password, ...agentData } = agent;
    const agentBookings = bookingsData.filter(b => b.agentId === id);
    
    const agentWithStats = {
      ...agentData,
      stats: {
        totalBookings: agentBookings.length,
        activeBookings: agentBookings.filter(b => b.status === 'confirmed').length,
        revenue: agentBookings.reduce((sum, b) => sum + b.totalAmount, 0),
        monthlyBookings: agentBookings.filter(b => {
          const bookingDate = new Date(b.bookingDate);
          const currentMonth = new Date();
          return bookingDate.getMonth() === currentMonth.getMonth() &&
                 bookingDate.getFullYear() === currentMonth.getFullYear();
        }).length,
      }
    };

    res.json({
      success: true,
      data: agentWithStats,
    });
  } catch (error) {
    next(error);
  }
});

// Create new agent (admin only)
router.post('/', authenticateToken, authorizeRoles('super_admin'), (req, res, next) => {
  try {
    const agentData = req.body;
    
    // Check if email already exists
    const existingUser = usersData.find(u => u.email === agentData.email);
    if (existingUser) {
      return next(createError('Email already exists', 400));
    }

    const newAgent = {
      id: `agent-${Date.now()}`,
      role: 'travel_agent',
      ...agentData,
      totalBookings: 0,
      revenue: 0,
      rating: 0,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // In a real app, this would save to database
    const { password, ...agentWithoutPassword } = newAgent;

    res.status(201).json({
      success: true,
      data: agentWithoutPassword,
      message: 'Agent created successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Update agent
router.put('/:id', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const agentIndex = usersData.findIndex(u => u.id === id && u.role === 'travel_agent');
    if (agentIndex === -1) {
      return next(createError('Agent not found', 404));
    }

    // Check permissions
    if (req.user?.role !== 'super_admin' && req.user?.id !== id) {
      return next(createError('Access denied', 403));
    }

    const updatedAgent = {
      ...usersData[agentIndex],
      ...updateData,
      updatedAt: new Date().toISOString(),
    };

    // In a real app, this would update the database
    const { password, ...agentWithoutPassword } = updatedAgent;

    res.json({
      success: true,
      data: agentWithoutPassword,
      message: 'Agent updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Delete agent (admin only)
router.delete('/:id', authenticateToken, authorizeRoles('super_admin'), (req, res, next) => {
  try {
    const { id } = req.params;
    
    const agentIndex = usersData.findIndex(u => u.id === id && u.role === 'travel_agent');
    if (agentIndex === -1) {
      return next(createError('Agent not found', 404));
    }

    // In a real app, this would delete from database
    res.json({
      success: true,
      message: 'Agent deleted successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Get agent statistics (admin only)
router.get('/stats/overview', authenticateToken, authorizeRoles('super_admin'), (req, res, next) => {
  try {
    const agents = usersData.filter(u => u.role === 'travel_agent');
    const activeAgents = agents.filter(a => (a as any).isActive);
    const totalBookings = bookingsData.length;
    const totalRevenue = bookingsData.reduce((sum, b) => sum + b.totalAmount, 0);
    
    // Calculate monthly growth (mock calculation)
    const monthlyGrowth = 12.5; // Mock percentage

    res.json({
      success: true,
      data: {
        totalAgents: agents.length,
        activeAgents: activeAgents.length,
        totalBookings,
        totalRevenue,
        monthlyGrowth,
      },
    });
  } catch (error) {
    next(error);
  }
});

export default router;
