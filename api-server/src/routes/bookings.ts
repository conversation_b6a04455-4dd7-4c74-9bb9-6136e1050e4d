import express from 'express';
import { authenticateToken, authorizeRoles, AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import bookingsData from '../data/bookings.json';
import packagesData from '../data/packages.json';
import usersData from '../data/users.json';

const router = express.Router();

// Get all bookings (with role-based filtering)
router.get('/', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const { page = 1, limit = 10, status, agentId, consumerId } = req.query;
    
    let bookings = [...bookingsData];
    
    // Role-based filtering
    if (req.user?.role === 'travel_agent') {
      bookings = bookings.filter(booking => booking.agentId === req.user?.id);
    } else if (req.user?.role === 'consumer') {
      bookings = bookings.filter(booking => booking.consumerId === req.user?.id);
    }

    // Additional filters
    if (status) {
      bookings = bookings.filter(booking => booking.status === status);
    }
    if (agentId && req.user?.role === 'super_admin') {
      bookings = bookings.filter(booking => booking.agentId === agentId);
    }
    if (consumerId && req.user?.role !== 'consumer') {
      bookings = bookings.filter(booking => booking.consumerId === consumerId);
    }

    // Pagination
    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedBookings = bookings.slice(startIndex, endIndex);

    // Enrich bookings with package and user data
    const enrichedBookings = paginatedBookings.map(booking => {
      const package_ = packagesData.find(pkg => pkg.id === booking.packageId);
      const consumer = usersData.find(user => user.id === booking.consumerId);
      const agent = usersData.find(user => user.id === booking.agentId);

      return {
        ...booking,
        package: package_ ? {
          id: package_.id,
          name: package_.name,
          destination: package_.destination,
          duration: package_.duration,
          price: package_.price,
        } : null,
        consumer: consumer ? {
          id: consumer.id,
          firstName: consumer.firstName,
          lastName: consumer.lastName,
          email: consumer.email,
        } : null,
        agent: agent ? {
          id: agent.id,
          firstName: agent.firstName,
          lastName: agent.lastName,
          email: agent.email,
          agencyName: (agent as any).agencyName,
        } : null,
      };
    });

    res.json({
      success: true,
      data: {
        bookings: enrichedBookings,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: bookings.length,
          pages: Math.ceil(bookings.length / Number(limit)),
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

// Get booking by ID
router.get('/:id', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const booking = bookingsData.find(b => b.id === id);
    
    if (!booking) {
      return next(createError('Booking not found', 404));
    }

    // Check permissions
    if (req.user?.role === 'travel_agent' && booking.agentId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }
    if (req.user?.role === 'consumer' && booking.consumerId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }

    // Enrich booking with package and user data
    const package_ = packagesData.find(pkg => pkg.id === booking.packageId);
    const consumer = usersData.find(user => user.id === booking.consumerId);
    const agent = usersData.find(user => user.id === booking.agentId);

    const enrichedBooking = {
      ...booking,
      package: package_,
      consumer: consumer ? {
        id: consumer.id,
        firstName: consumer.firstName,
        lastName: consumer.lastName,
        email: consumer.email,
        phone: consumer.phone,
      } : null,
      agent: agent ? {
        id: agent.id,
        firstName: agent.firstName,
        lastName: agent.lastName,
        email: agent.email,
        phone: agent.phone,
        agencyName: (agent as any).agencyName,
      } : null,
    };

    res.json({
      success: true,
      data: enrichedBooking,
    });
  } catch (error) {
    next(error);
  }
});

// Create new booking (consumers only)
router.post('/', authenticateToken, authorizeRoles('consumer'), (req: AuthRequest, res, next) => {
  try {
    const bookingData = req.body;
    
    // Verify package exists
    const package_ = packagesData.find(pkg => pkg.id === bookingData.packageId);
    if (!package_) {
      return next(createError('Package not found', 404));
    }

    const newBooking = {
      id: `booking-${Date.now()}`,
      ...bookingData,
      consumerId: req.user?.id,
      agentId: package_.agentId,
      status: 'pending',
      paymentStatus: 'pending',
      paidAmount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // In a real app, this would save to database
    res.status(201).json({
      success: true,
      data: newBooking,
      message: 'Booking created successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Update booking status (agents and admins)
router.put('/:id', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const bookingIndex = bookingsData.findIndex(b => b.id === id);
    if (bookingIndex === -1) {
      return next(createError('Booking not found', 404));
    }

    const booking = bookingsData[bookingIndex];

    // Check permissions
    if (req.user?.role === 'travel_agent' && booking.agentId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }
    if (req.user?.role === 'consumer' && booking.consumerId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }

    // Consumers can only update certain fields
    if (req.user?.role === 'consumer') {
      const allowedFields = ['specialRequests', 'travelers'];
      const filteredUpdateData = Object.keys(updateData)
        .filter(key => allowedFields.includes(key))
        .reduce((obj, key) => {
          obj[key] = updateData[key];
          return obj;
        }, {} as any);
      updateData = filteredUpdateData;
    }

    const updatedBooking = {
      ...booking,
      ...updateData,
      updatedAt: new Date().toISOString(),
    };

    // In a real app, this would update the database
    res.json({
      success: true,
      data: updatedBooking,
      message: 'Booking updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Cancel booking
router.delete('/:id', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    
    const bookingIndex = bookingsData.findIndex(b => b.id === id);
    if (bookingIndex === -1) {
      return next(createError('Booking not found', 404));
    }

    const booking = bookingsData[bookingIndex];

    // Check permissions
    if (req.user?.role === 'travel_agent' && booking.agentId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }
    if (req.user?.role === 'consumer' && booking.consumerId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }

    // In a real app, this would update the booking status to cancelled
    res.json({
      success: true,
      message: 'Booking cancelled successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Get booking statistics
router.get('/stats/overview', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    let bookings = [...bookingsData];
    
    // Filter by role
    if (req.user?.role === 'travel_agent') {
      bookings = bookings.filter(booking => booking.agentId === req.user?.id);
    } else if (req.user?.role === 'consumer') {
      bookings = bookings.filter(booking => booking.consumerId === req.user?.id);
    }

    const totalBookings = bookings.length;
    const confirmedBookings = bookings.filter(b => b.status === 'confirmed').length;
    const pendingBookings = bookings.filter(b => b.status === 'pending').length;
    const totalRevenue = bookings.reduce((sum, b) => sum + b.totalAmount, 0);
    const paidRevenue = bookings.reduce((sum, b) => sum + b.paidAmount, 0);

    res.json({
      success: true,
      data: {
        totalBookings,
        confirmedBookings,
        pendingBookings,
        totalRevenue,
        paidRevenue,
        pendingRevenue: totalRevenue - paidRevenue,
      },
    });
  } catch (error) {
    next(error);
  }
});

export default router;
