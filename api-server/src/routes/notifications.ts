import express from 'express';
import { authenticateToken, authorizeR<PERSON>s, AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import notificationsData from '../data/notifications.json';

const router = express.Router();

// Get notifications for current user
router.get('/', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const { page = 1, limit = 20, isRead, type } = req.query;
    
    let notifications = notificationsData.filter(n => n.recipientId === req.user?.id);
    
    // Filter by read status
    if (isRead !== undefined) {
      notifications = notifications.filter(n => n.isRead === (isRead === 'true'));
    }

    // Filter by type
    if (type) {
      notifications = notifications.filter(n => n.type === type);
    }

    // Sort by creation date (newest first)
    notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Pagination
    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedNotifications = notifications.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        notifications: paginatedNotifications,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: notifications.length,
          pages: Math.ceil(notifications.length / Number(limit)),
        },
        unreadCount: notifications.filter(n => !n.isRead).length,
      },
    });
  } catch (error) {
    next(error);
  }
});

// Get notification by ID
router.get('/:id', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const notification = notificationsData.find(n => n.id === id);
    
    if (!notification) {
      return next(createError('Notification not found', 404));
    }

    // Check if user owns this notification
    if (notification.recipientId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }

    res.json({
      success: true,
      data: notification,
    });
  } catch (error) {
    next(error);
  }
});

// Mark notification as read
router.put('/:id/read', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const notificationIndex = notificationsData.findIndex(n => n.id === id);
    
    if (notificationIndex === -1) {
      return next(createError('Notification not found', 404));
    }

    const notification = notificationsData[notificationIndex];

    // Check if user owns this notification
    if (notification.recipientId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }

    // In a real app, this would update the database
    const updatedNotification = {
      ...notification,
      isRead: true,
    };

    res.json({
      success: true,
      data: updatedNotification,
      message: 'Notification marked as read',
    });
  } catch (error) {
    next(error);
  }
});

// Mark all notifications as read
router.put('/read-all', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    // In a real app, this would update all user's notifications in the database
    const userNotifications = notificationsData.filter(n => n.recipientId === req.user?.id);
    const unreadCount = userNotifications.filter(n => !n.isRead).length;

    res.json({
      success: true,
      message: `${unreadCount} notifications marked as read`,
    });
  } catch (error) {
    next(error);
  }
});

// Send notification (agents and admins)
router.post('/send', authenticateToken, authorizeRoles('travel_agent', 'super_admin'), (req: AuthRequest, res, next) => {
  try {
    const { recipientId, type, title, message, priority = 'medium', scheduledFor } = req.body;

    if (!recipientId || !type || !title || !message) {
      return next(createError('Missing required fields', 400));
    }

    const newNotification = {
      id: `notif-${Date.now()}`,
      recipientId,
      senderId: req.user?.id,
      type,
      title,
      message,
      isRead: false,
      priority,
      createdAt: new Date().toISOString(),
      scheduledFor: scheduledFor || new Date().toISOString(),
    };

    // In a real app, this would save to database and potentially trigger push notifications
    res.status(201).json({
      success: true,
      data: newNotification,
      message: 'Notification sent successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Send bulk notifications (admins only)
router.post('/send-bulk', authenticateToken, authorizeRoles('super_admin'), (req: AuthRequest, res, next) => {
  try {
    const { recipientIds, type, title, message, priority = 'medium' } = req.body;

    if (!recipientIds || !Array.isArray(recipientIds) || !type || !title || !message) {
      return next(createError('Missing required fields or invalid recipientIds', 400));
    }

    const notifications = recipientIds.map(recipientId => ({
      id: `notif-${Date.now()}-${recipientId}`,
      recipientId,
      senderId: req.user?.id,
      type,
      title,
      message,
      isRead: false,
      priority,
      createdAt: new Date().toISOString(),
      scheduledFor: new Date().toISOString(),
    }));

    // In a real app, this would save to database and trigger push notifications
    res.status(201).json({
      success: true,
      data: notifications,
      message: `${notifications.length} notifications sent successfully`,
    });
  } catch (error) {
    next(error);
  }
});

// Delete notification
router.delete('/:id', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const notificationIndex = notificationsData.findIndex(n => n.id === id);
    
    if (notificationIndex === -1) {
      return next(createError('Notification not found', 404));
    }

    const notification = notificationsData[notificationIndex];

    // Check if user owns this notification
    if (notification.recipientId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }

    // In a real app, this would delete from database
    res.json({
      success: true,
      message: 'Notification deleted successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Get notification statistics
router.get('/stats/overview', authenticateToken, (req: AuthRequest, res, next) => {
  try {
    const userNotifications = notificationsData.filter(n => n.recipientId === req.user?.id);
    
    const totalNotifications = userNotifications.length;
    const unreadNotifications = userNotifications.filter(n => !n.isRead).length;
    const highPriorityUnread = userNotifications.filter(n => !n.isRead && n.priority === 'high').length;
    
    // Group by type
    const notificationsByType = userNotifications.reduce((acc, notification) => {
      acc[notification.type] = (acc[notification.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    res.json({
      success: true,
      data: {
        totalNotifications,
        unreadNotifications,
        highPriorityUnread,
        notificationsByType,
      },
    });
  } catch (error) {
    next(error);
  }
});

export default router;
