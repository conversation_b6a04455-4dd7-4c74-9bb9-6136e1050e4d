import express from 'express';
import { authenticateToken, authorizeR<PERSON>s, AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import packagesData from '../data/packages.json';

const router = express.Router();

// Get all packages
router.get('/', (req, res, next) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search, 
      category, 
      destination, 
      minPrice, 
      maxPrice,
      agentId 
    } = req.query;
    
    let packages = [...packagesData];
    
    // Filter by agent if specified
    if (agentId) {
      packages = packages.filter(pkg => pkg.agentId === agentId);
    }

    // Search filter
    if (search) {
      const searchTerm = String(search).toLowerCase();
      packages = packages.filter(pkg => 
        pkg.name.toLowerCase().includes(searchTerm) ||
        pkg.description.toLowerCase().includes(searchTerm) ||
        pkg.destination.toLowerCase().includes(searchTerm)
      );
    }

    // Category filter
    if (category) {
      packages = packages.filter(pkg => pkg.category === category);
    }

    // Destination filter
    if (destination) {
      packages = packages.filter(pkg => 
        pkg.destination.toLowerCase().includes(String(destination).toLowerCase())
      );
    }

    // Price range filter
    if (minPrice) {
      packages = packages.filter(pkg => pkg.price >= Number(minPrice));
    }
    if (maxPrice) {
      packages = packages.filter(pkg => pkg.price <= Number(maxPrice));
    }

    // Only show active packages for non-agents
    packages = packages.filter(pkg => pkg.isActive);

    // Pagination
    const startIndex = (Number(page) - 1) * Number(limit);
    const endIndex = startIndex + Number(limit);
    const paginatedPackages = packages.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        packages: paginatedPackages,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: packages.length,
          pages: Math.ceil(packages.length / Number(limit)),
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

// Get package by ID
router.get('/:id', (req, res, next) => {
  try {
    const { id } = req.params;
    const package_ = packagesData.find(pkg => pkg.id === id);
    
    if (!package_) {
      return next(createError('Package not found', 404));
    }

    res.json({
      success: true,
      data: package_,
    });
  } catch (error) {
    next(error);
  }
});

// Create new package (agents only)
router.post('/', authenticateToken, authorizeRoles('travel_agent'), (req: AuthRequest, res, next) => {
  try {
    const packageData = req.body;
    
    const newPackage = {
      id: `pkg-${Date.now()}`,
      ...packageData,
      agentId: req.user?.id,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // In a real app, this would save to database
    res.status(201).json({
      success: true,
      data: newPackage,
      message: 'Package created successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Update package (agents only - own packages)
router.put('/:id', authenticateToken, authorizeRoles('travel_agent'), (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const packageIndex = packagesData.findIndex(pkg => pkg.id === id);
    if (packageIndex === -1) {
      return next(createError('Package not found', 404));
    }

    const package_ = packagesData[packageIndex];
    
    // Check if agent owns this package
    if (package_.agentId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }

    const updatedPackage = {
      ...package_,
      ...updateData,
      updatedAt: new Date().toISOString(),
    };

    // In a real app, this would update the database
    res.json({
      success: true,
      data: updatedPackage,
      message: 'Package updated successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Delete package (agents only - own packages)
router.delete('/:id', authenticateToken, authorizeRoles('travel_agent'), (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;
    
    const packageIndex = packagesData.findIndex(pkg => pkg.id === id);
    if (packageIndex === -1) {
      return next(createError('Package not found', 404));
    }

    const package_ = packagesData[packageIndex];
    
    // Check if agent owns this package
    if (package_.agentId !== req.user?.id) {
      return next(createError('Access denied', 403));
    }

    // In a real app, this would delete from database
    res.json({
      success: true,
      message: 'Package deleted successfully',
    });
  } catch (error) {
    next(error);
  }
});

// Search packages with advanced filters
router.get('/search/advanced', (req, res, next) => {
  try {
    const { 
      query,
      categories,
      destinations,
      priceRange,
      duration,
      difficulty,
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;
    
    let packages = [...packagesData].filter(pkg => pkg.isActive);
    
    // Text search
    if (query) {
      const searchTerm = String(query).toLowerCase();
      packages = packages.filter(pkg => 
        pkg.name.toLowerCase().includes(searchTerm) ||
        pkg.description.toLowerCase().includes(searchTerm) ||
        pkg.destination.toLowerCase().includes(searchTerm)
      );
    }

    // Category filter
    if (categories) {
      const categoryList = String(categories).split(',');
      packages = packages.filter(pkg => categoryList.includes(pkg.category));
    }

    // Destination filter
    if (destinations) {
      const destinationList = String(destinations).split(',');
      packages = packages.filter(pkg => 
        destinationList.some(dest => 
          pkg.destination.toLowerCase().includes(dest.toLowerCase())
        )
      );
    }

    // Price range filter
    if (priceRange) {
      const [min, max] = String(priceRange).split('-').map(Number);
      packages = packages.filter(pkg => pkg.price >= min && pkg.price <= max);
    }

    // Duration filter
    if (duration) {
      const [minDays, maxDays] = String(duration).split('-').map(Number);
      packages = packages.filter(pkg => pkg.duration >= minDays && pkg.duration <= maxDays);
    }

    // Difficulty filter
    if (difficulty) {
      packages = packages.filter(pkg => pkg.difficulty === difficulty);
    }

    // Sorting
    packages.sort((a, b) => {
      let aVal, bVal;
      
      switch (sortBy) {
        case 'price':
          aVal = a.price;
          bVal = b.price;
          break;
        case 'duration':
          aVal = a.duration;
          bVal = b.duration;
          break;
        case 'name':
        default:
          aVal = a.name.toLowerCase();
          bVal = b.name.toLowerCase();
          break;
      }
      
      if (sortOrder === 'desc') {
        return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
      } else {
        return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
      }
    });

    res.json({
      success: true,
      data: {
        packages,
        total: packages.length,
      },
    });
  } catch (error) {
    next(error);
  }
});

export default router;
