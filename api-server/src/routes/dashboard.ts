import express from 'express';
import { authenticateToken, authorizeRoles, AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import usersData from '../data/users.json';
import packagesData from '../data/packages.json';
import bookingsData from '../data/bookings.json';
import notificationsData from '../data/notifications.json';

const router = express.Router();

// Admin dashboard statistics
router.get('/admin', authenticateToken, authorizeRoles('super_admin'), (req: AuthRequest, res, next) => {
  try {
    const agents = usersData.filter(u => u.role === 'travel_agent');
    const consumers = usersData.filter(u => u.role === 'consumer');
    const activeAgents = agents.filter(a => (a as any).isActive);
    
    const totalBookings = bookingsData.length;
    const confirmedBookings = bookingsData.filter(b => b.status === 'confirmed').length;
    const pendingBookings = bookingsData.filter(b => b.status === 'pending').length;
    
    const totalRevenue = bookingsData.reduce((sum, b) => sum + b.totalAmount, 0);
    const paidRevenue = bookingsData.reduce((sum, b) => sum + b.paidAmount, 0);
    
    // Calculate monthly growth (mock calculation)
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const currentMonthBookings = bookingsData.filter(b => {
      const bookingDate = new Date(b.bookingDate);
      return bookingDate.getMonth() === currentMonth && bookingDate.getFullYear() === currentYear;
    });
    
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    const lastMonthBookings = bookingsData.filter(b => {
      const bookingDate = new Date(b.bookingDate);
      return bookingDate.getMonth() === lastMonth && bookingDate.getFullYear() === lastMonthYear;
    });
    
    const monthlyGrowth = lastMonthBookings.length > 0 
      ? ((currentMonthBookings.length - lastMonthBookings.length) / lastMonthBookings.length) * 100
      : 0;

    // Recent activities
    const recentBookings = bookingsData
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)
      .map(booking => {
        const package_ = packagesData.find(p => p.id === booking.packageId);
        const consumer = usersData.find(u => u.id === booking.consumerId);
        const agent = usersData.find(u => u.id === booking.agentId);
        
        return {
          id: booking.id,
          type: 'booking',
          title: `New booking for ${package_?.name}`,
          description: `${consumer?.firstName} ${consumer?.lastName} booked ${package_?.name}`,
          timestamp: booking.createdAt,
          status: booking.status,
          amount: booking.totalAmount,
        };
      });

    // Top performing agents
    const agentPerformance = agents.map(agent => {
      const agentBookings = bookingsData.filter(b => b.agentId === agent.id);
      const revenue = agentBookings.reduce((sum, b) => sum + b.totalAmount, 0);
      
      return {
        id: agent.id,
        name: `${agent.firstName} ${agent.lastName}`,
        agencyName: (agent as any).agencyName,
        bookings: agentBookings.length,
        revenue,
        rating: (agent as any).rating || 0,
      };
    }).sort((a, b) => b.revenue - a.revenue).slice(0, 5);

    res.json({
      success: true,
      data: {
        overview: {
          totalAgents: agents.length,
          activeAgents: activeAgents.length,
          totalConsumers: consumers.length,
          totalPackages: packagesData.length,
          totalBookings,
          confirmedBookings,
          pendingBookings,
          totalRevenue,
          paidRevenue,
          pendingRevenue: totalRevenue - paidRevenue,
          monthlyGrowth,
        },
        recentActivities: recentBookings,
        topAgents: agentPerformance,
        chartData: {
          monthlyBookings: [
            { month: 'Jan', bookings: 12, revenue: 24000 },
            { month: 'Feb', bookings: 19, revenue: 38000 },
            { month: 'Mar', bookings: 15, revenue: 30000 },
            { month: 'Apr', bookings: 22, revenue: 44000 },
            { month: 'May', bookings: 18, revenue: 36000 },
            { month: 'Jun', bookings: 25, revenue: 50000 },
          ],
          bookingsByStatus: [
            { status: 'Confirmed', count: confirmedBookings },
            { status: 'Pending', count: pendingBookings },
            { status: 'Cancelled', count: bookingsData.filter(b => b.status === 'cancelled').length },
            { status: 'Completed', count: bookingsData.filter(b => b.status === 'completed').length },
          ],
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

// Agent dashboard statistics
router.get('/agent', authenticateToken, authorizeRoles('travel_agent'), (req: AuthRequest, res, next) => {
  try {
    const agentId = req.user?.id;
    const agentPackages = packagesData.filter(p => p.agentId === agentId);
    const agentBookings = bookingsData.filter(b => b.agentId === agentId);
    
    const activeBookings = agentBookings.filter(b => b.status === 'confirmed').length;
    const pendingBookings = agentBookings.filter(b => b.status === 'pending').length;
    const completedBookings = agentBookings.filter(b => b.status === 'completed').length;
    
    const monthlyRevenue = agentBookings
      .filter(b => {
        const bookingDate = new Date(b.bookingDate);
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        return bookingDate.getMonth() === currentMonth && bookingDate.getFullYear() === currentYear;
      })
      .reduce((sum, b) => sum + b.totalAmount, 0);
    
    const totalRevenue = agentBookings.reduce((sum, b) => sum + b.totalAmount, 0);
    
    // Get unique customers
    const uniqueCustomers = new Set(agentBookings.map(b => b.consumerId));
    const customerCount = uniqueCustomers.size;
    
    // Recent bookings
    const recentBookings = agentBookings
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5)
      .map(booking => {
        const package_ = packagesData.find(p => p.id === booking.packageId);
        const consumer = usersData.find(u => u.id === booking.consumerId);
        
        return {
          id: booking.id,
          packageName: package_?.name,
          customerName: `${consumer?.firstName} ${consumer?.lastName}`,
          travelDate: booking.travelDate,
          status: booking.status,
          amount: booking.totalAmount,
          travelers: booking.numberOfTravelers,
        };
      });

    // Package performance
    const packagePerformance = agentPackages.map(package_ => {
      const packageBookings = agentBookings.filter(b => b.packageId === package_.id);
      const revenue = packageBookings.reduce((sum, b) => sum + b.totalAmount, 0);
      
      return {
        id: package_.id,
        name: package_.name,
        destination: package_.destination,
        bookings: packageBookings.length,
        revenue,
        price: package_.price,
      };
    }).sort((a, b) => b.bookings - a.bookings).slice(0, 5);

    res.json({
      success: true,
      data: {
        overview: {
          totalPackages: agentPackages.length,
          activePackages: agentPackages.filter(p => p.isActive).length,
          totalBookings: agentBookings.length,
          activeBookings,
          pendingBookings,
          completedBookings,
          monthlyRevenue,
          totalRevenue,
          customerCount,
          averageRating: 4.8, // Mock rating
        },
        recentBookings,
        topPackages: packagePerformance,
        chartData: {
          monthlyBookings: [
            { month: 'Jan', bookings: 3, revenue: 6000 },
            { month: 'Feb', bookings: 5, revenue: 10000 },
            { month: 'Mar', bookings: 4, revenue: 8000 },
            { month: 'Apr', bookings: 7, revenue: 14000 },
            { month: 'May', bookings: 6, revenue: 12000 },
            { month: 'Jun', bookings: 8, revenue: 16000 },
          ],
          bookingsByStatus: [
            { status: 'Confirmed', count: activeBookings },
            { status: 'Pending', count: pendingBookings },
            { status: 'Completed', count: completedBookings },
          ],
        },
      },
    });
  } catch (error) {
    next(error);
  }
});

// Consumer dashboard statistics
router.get('/consumer', authenticateToken, authorizeRoles('consumer'), (req: AuthRequest, res, next) => {
  try {
    const consumerId = req.user?.id;
    const consumerBookings = bookingsData.filter(b => b.consumerId === consumerId);
    
    const upcomingTrips = consumerBookings.filter(b => {
      const travelDate = new Date(b.travelDate);
      const now = new Date();
      return travelDate > now && (b.status === 'confirmed' || b.status === 'pending');
    });
    
    const completedTrips = consumerBookings.filter(b => b.status === 'completed');
    const totalSpent = consumerBookings.reduce((sum, b) => sum + b.paidAmount, 0);
    
    // Upcoming trips with details
    const upcomingTripsDetails = upcomingTrips.map(booking => {
      const package_ = packagesData.find(p => p.id === booking.packageId);
      const agent = usersData.find(u => u.id === booking.agentId);
      
      return {
        id: booking.id,
        packageName: package_?.name,
        destination: package_?.destination,
        duration: package_?.duration,
        travelDate: booking.travelDate,
        status: booking.status,
        travelers: booking.numberOfTravelers,
        agent: {
          name: `${agent?.firstName} ${agent?.lastName}`,
          agencyName: (agent as any)?.agencyName,
          phone: agent?.phone,
        },
      };
    }).sort((a, b) => new Date(a.travelDate).getTime() - new Date(b.travelDate).getTime());

    // Recent notifications
    const recentNotifications = notificationsData
      .filter(n => n.recipientId === consumerId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);

    res.json({
      success: true,
      data: {
        overview: {
          upcomingTrips: upcomingTrips.length,
          completedTrips: completedTrips.length,
          totalSpent,
          savedPackages: 0, // Mock data
          unreadNotifications: recentNotifications.filter(n => !n.isRead).length,
        },
        upcomingTrips: upcomingTripsDetails,
        recentNotifications,
        travelHistory: completedTrips.map(booking => {
          const package_ = packagesData.find(p => p.id === booking.packageId);
          return {
            id: booking.id,
            packageName: package_?.name,
            destination: package_?.destination,
            travelDate: booking.travelDate,
            duration: package_?.duration,
            amount: booking.totalAmount,
          };
        }).slice(0, 5),
      },
    });
  } catch (error) {
    next(error);
  }
});

export default router;
