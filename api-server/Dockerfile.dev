FROM node:18-alpine

WORKDIR /app

# Install curl and nodemon for development
RUN apk add --no-cache curl
RUN npm install -g nodemon ts-node

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy source code
COPY . .

# Create uploads directory
RUN mkdir -p uploads/avatars uploads/packages

# Expose port
EXPOSE 3000

# Start with nodemon for hot reload
CMD ["npm", "run", "dev"]
